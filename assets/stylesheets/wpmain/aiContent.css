.ebook-container {
    max-width: 1400px;
    padding: 20px;
    min-height: calc(100vh - 160px);
}

/* Header Styles */
.ebook-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #333;
    padding: 25px 30px;
    margin-bottom: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #667eea;
}

.book-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    text-align: center;
}

.book-title::before {
    content: "📚";
    font-size: 1.5rem;
}

/* Layout Styles */
.ebook-layout {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Chapter Selection */
.chapter-selection {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #28a745;
}

.chapter-selection h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chapter-selection h3::before {
    content: "📖";
    font-size: 1.1rem;
}

.chapter-dropdown-wrapper {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.chapter-dropdown {
    width: 100%;
    padding: 12px 40px 12px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white !important;
    color: #333 !important;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.chapter-dropdown-wrapper::after {
    content: "▼";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #667eea;
    pointer-events: none;
    font-size: 0.8rem;
}

/* Preview Mode Styles */
.chapter-option-locked {
    color: #999 !important;
    background: #f5f5f5 !important;
    cursor: not-allowed !important;
}

.chapter-option-locked::before {
    content: "🔒 ";
    margin-right: 5px;
}

.chapter-dropdown option:disabled {
    color: #999 !important;
    background: #f5f5f5 !important;
}

.chapter-dropdown option {
    color: #333 !important;
    background: white !important;
    padding: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.chapter-dropdown::-webkit-input-placeholder {
    color: #666 !important;
}

.chapter-dropdown::-moz-placeholder {
    color: #666 !important;
}

.chapter-dropdown:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chapter-dropdown:hover {
    border-color: #667eea;
}

/* Content Area */
.content-area {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

/* Loading Spinner */
.loading-spinner {
    display: none;
    text-align: center;
    padding: 40px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chapter Summary */
.chapter-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #667eea;
}

.summary-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 12px;
}

.summary-title::before {
    content: "📊";
    font-size: 1.3rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.question-type-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
}

.type-nav-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    gap: 15px;
}

.type-nav-item:hover {
    background: #f0f0f0;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #e9ecef;
}

.type-nav-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    flex-shrink: 0;
}

.type-nav-content {
    flex: 1;
}

.type-nav-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
    font-size: 1rem;
}

.type-nav-count {
    font-size: 0.9rem;
    color: #666;
}

/* Icon Colors */
.icon-exercise { background: #4CAF50; }
.icon-long-answer { background: #2196F3; }
.icon-short-answer { background: #FF9800; }
.icon-very-short { background: #9C27B0; }
.icon-assertion { background: #607D8B; }
.icon-problem { background: #795548; }
.icon-mcq { background: #F44336; }
.icon-fill-blank { background: #3F51B5; }
.icon-true-false { background: #009688; }
.icon-match { background: #E91E63; }
.icon-sequence { background: #FF5722; }

/* Content Sections */
.content-section {
    margin-bottom: 40px;
}

.section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.section-header h2 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.section-description {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* Question Styles */
.question-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.question-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.question-item-highlighted {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5) !important;
    border: 2px solid #667eea !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
    transform: translateY(-2px);
    animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
    }
}

.question-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 15px;
    line-height: 1.6;
}

.answer-text {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    font-size: 1rem;
    line-height: 1.6;
}

.question-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-explanation {
    background: #17a2b8;
    color: white;
}

.btn-explanation:hover {
    background: #138496;
    transform: translateY(-1px);
}

.btn-create-more {
    background: #28a745;
    color: white;
}

.btn-create-more:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-ask-doubt {
    background: #ffc107;
    color: #212529;
}

.btn-ask-doubt:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.btn-feedback {
    background: #ff9800;
    color: white;
}

.btn-feedback:hover {
    background: #f57c00;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-danger:disabled {
    background: #dc3545;
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Explanation Styles */
.explanation-container {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
    display: none;
    animation: slideDown 0.3s ease;
}

.explanation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.explanation-title {
    font-weight: 600;
    color: #0c5460;
    margin: 0;
}

.close-explanation {
    background: none;
    border: none;
    color: #0c5460;
    cursor: pointer;
    font-size: 1.2rem;
}

.explanation-text {
    color: #0c5460;
    line-height: 1.6;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 500px;
    }
}

/* Question Type Sections */
.question-type-section {
    margin-bottom: 30px;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
}

.question-type-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.question-type-header:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

.question-type-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
}

.question-type-count {
    background: rgba(255,255,255,0.2);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
}

.question-type-header-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.mcq-header-actions {
    display: flex;
    gap: 8px;
    margin-right: 15px;
}

.mcq-header-btn {
    background: #4CAF50;
    color: white;
    border: 1px solid #45a049;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.mcq-header-btn:hover {
    background: #45a049;
    border-color: #3d8b40;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.mcq-header-btn:active {
    transform: translateY(0);
}

/* Specific button colors */
.mcq-btn-play {
    background: #2196F3;
    border-color: #1976D2;
}

.mcq-btn-play:hover {
    background: #1976D2;
    border-color: #1565C0;
}

.mcq-btn-practice {
    background: #FF9800;
    border-color: #F57C00;
}

.mcq-btn-practice:hover {
    background: #F57C00;
    border-color: #E65100;
}

.mcq-btn-test {
    background: #9C27B0;
    border-color: #7B1FA2;
}

.mcq-btn-test:hover {
    background: #7B1FA2;
    border-color: #6A1B9A;
}

.question-type-content {
    padding: 25px;
    display: none;
}

.question-type-content.open {
    display: block;
    min-height: 60vh;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

/* MCQ Specific Styles */
.mcq-options {
    margin: 15px 0;
}

.mcq-option {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px 15px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mcq-option.correct {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.option-label {
    font-weight: 600;
    min-width: 25px;
}

/* MCQ Action Buttons */
.mcq-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.mcq-action-btn {
    padding: 8px 16px;
    border: 1px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.mcq-action-btn:hover {
    background: #667eea;
    color: white;
}

/* Toast Notification */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    z-index: 99999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

/* No Data Message */
.no-data-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-data-icon {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 15px;
}

/* Go to Top Button */
.go-to-top {
    position: fixed;
    bottom: 120px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.go-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.go-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Feedback Modal Styles */
.feedback-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.feedback-modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feedback-modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.feedback-modal-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.feedback-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.feedback-modal-close:hover {
    background-color: rgba(255,255,255,0.2);
}

.feedback-modal-body {
    padding: 25px;
}

.feedback-options {
    margin-bottom: 20px;
}

.feedback-option {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.feedback-option:hover {
    background-color: #f8f9fa;
}

.feedback-option input[type="radio"] {
    margin-right: 12px;
    transform: scale(1.2);
}

.feedback-option label {
    cursor: pointer;
    font-weight: 500;
    color: #333;
}

.feedback-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.95rem;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.feedback-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.feedback-modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.feedback-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.feedback-btn-cancel {
    background: #f8f9fa;
    color: #666;
}

.feedback-btn-cancel:hover {
    background: #e9ecef;
}

.feedback-btn-submit {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.feedback-btn-submit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.feedback-btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Buy Now Button Styles */
.gptBuyNowBtn {
    background: #3b82f6;
    color: white;
    height: 100%;
    width: 150px;
    border: none;
}

.gptBuyNowBtn:hover {
    background: #2563eb;
    border-color: #2563eb;
    color: white;
    text-decoration: none;
}

.gptBuyNowBtn:active {
    transform: translateY(0);
}

.gptBuyNowBtn::before {
    content: "\f07a";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    font-size: 1.1rem;
    color: white;
    margin-right: 4px;
}

/* Chapter Selection Layout */
.chapter-selection-content {
    display: flex;
    align-items: flex-end;
    gap: 20px;
    flex-wrap: wrap;
    width: fit-content;
}

.chapter-dropdown-section {
    flex: 1;
    min-width: 300px;
}

.buy-now-section {
    flex-shrink: 0;
    height: 50px;
}

/* Question Number Styling */
.question-number {
    font-weight: 700;
    color: #3b82f6;
    margin-right: 8px;
    font-size: 1.1em;
}

/* Section Loading Spinner */
.section-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
}

.section-loader .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.section-loader p {
    margin: 0;
    font-size: 14px;
    color: #888;
}

/* Error message styling */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #dc3545;
    text-align: center;
}

.error-icon {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #dc3545;
}

/* Ensure content sections have adequate height without disrupting layout */
#exerciseSolutionsContainer,
#questionTypesContainer {
    min-height: 50vh;
}

/* Content sections should have proper spacing */
.content-section {
    margin-bottom: 30px;
}

.content-section:last-child {
    margin-bottom: 0;
}



/* Responsive Design */
@media (max-width: 768px) {
    .book-title {
        font-size: 2rem;
    }

    .ebook-container {
        padding: 15px;
    }

    .question-type-nav {
        grid-template-columns: 1fr;
    }

    .question-actions {
        flex-direction: column;
    }

    .action-btn {
        text-align: center;
        justify-content: center;
    }

    .chapter-dropdown-wrapper {
        max-width: 100%;
    }

    .type-nav-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .go-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .chapter-selection-content {
        flex-direction: column;
        align-items: stretch;
    }

    .chapter-dropdown-section {
        min-width: auto;
    }

    .gptBuyNowBtn {
        margin-top: 15px;
        width: 100%;
    }
}
