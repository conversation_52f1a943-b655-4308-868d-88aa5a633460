

// Main initialization function
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    initSmoothScrolling();

    // Button interaction effects
    initButtonEffects();

    // Initialize mind map animations
    initMindMap();

    // Initialize AI features animations
    initAIFeatures();

    // Initialize FAQ accordion
    initFAQ();

    // Initialize learning modes
    initLearningModes();

    // Initialize testimonials
    initTestimonials();

    // Initialize pricing section
    initPricing();

    // Initialize categories section
    initCategories();

    // Initialize pre-footer section
    initPreFooter();

    // Initialize footer section
    initFooter();

    // Add scroll-based animations
    initScrollAnimations();

    // Initialize sticky header
    initStickyHeader();
});

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Button interaction effects
function initButtonEffects() {
    const buttons = document.querySelectorAll('.cta-button, .signup-btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add a subtle animation effect
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

// Initialize mind map animations
function initMindMap() {
    const featureNodes = document.querySelectorAll('.feature-node');
    const rotatingContainer = document.querySelector('.features-rotating-container');
    let currentRotationStep = 0;

    // Initial animation on page load - show all features in a circle
    featureNodes.forEach(node => {
        const delay = parseInt(node.getAttribute('data-delay')) || 0;

        setTimeout(() => {
            node.classList.add('active');
        }, delay);
    });

    // Set up rotation interval (every 10 seconds)
    if (rotatingContainer) {
        let rotationInterval;

        const startRotation = () => {
            rotationInterval = setInterval(() => {
                // Remove previous rotation class if any
                if (currentRotationStep > 0) {
                    rotatingContainer.classList.remove(`rotate-step-${currentRotationStep}`);
                }

                // Update rotation step (cycle through 1-4)
                currentRotationStep = (currentRotationStep % 4) + 1;
                // Apply new rotation class
                rotatingContainer.classList.add(`rotate-step-${currentRotationStep}`);

                console.log(`Rotated to step ${currentRotationStep}`);
            }, 10000); // 10 seconds interval
        };

        // Start rotation after initial 10 seconds (only on desktop/tablet)
        function checkAndStartRotation() {
            if (window.innerWidth > 768) {
                setTimeout(startRotation, 3000);
            }
        }

        // Initial check
        checkAndStartRotation();

        // Check on window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768 && rotationInterval) {
                clearInterval(rotationInterval);
                rotatingContainer.className = 'features-rotating-container'; // Reset rotation classes
            } else if (window.innerWidth > 768 && !rotationInterval) {
                checkAndStartRotation();
            }
        });
    }

    // Hover effects removed as requested
}

// Initialize AI features animations
function initAIFeatures() {
    const featureItems = document.querySelectorAll('.feature-item');
    const tableRows = document.querySelectorAll('.table-row');
    const tableNote = document.querySelector('.table-note');

    // Function to animate elements when they come into view
    function animateOnScroll() {
        // Animate feature items
        featureItems.forEach(item => {
            if (isInViewport(item)) {
                const delay = parseInt(item.getAttribute('data-delay')) || 0;
                setTimeout(() => {
                    item.classList.add('active');
                }, delay);
            }
        });

        // Animate table rows
        tableRows.forEach(row => {
            if (isInViewport(row)) {
                const delay = parseInt(row.getAttribute('data-delay')) || 0;
                setTimeout(() => {
                    row.classList.add('active');
                }, delay);
            }
        });

        // Animate table note
        if (tableNote && isInViewport(tableNote)) {
            setTimeout(() => {
                tableNote.classList.add('active');
            }, 600); // Delay after table rows
        }
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Initial check
    animateOnScroll();

    // Check on scroll
    window.addEventListener('scroll', animateOnScroll);
}

// Initialize learning modes carousel
function initLearningModes() {
    const carouselTrack = document.getElementById('learningModesTrack');
    const modeCards = document.querySelectorAll('.mode-card');
    const dots = document.querySelectorAll('.carousel-dots .dot');
    let currentSlide = 0;
    let autoSlideInterval;

    if (!carouselTrack || modeCards.length === 0 || dots.length === 0) return;

    function goToSlide(slideIndex) {
        // Ensure slideIndex is within bounds
        slideIndex = Math.max(0, Math.min(slideIndex, modeCards.length - 1));

        // Update carousel position - each card is 33.333% width
        const translateX = -slideIndex * 33.333;
        carouselTrack.style.transform = `translateX(${translateX}%)`;

        // Update active states
        modeCards.forEach((card, index) => {
            card.classList.toggle('active', index === slideIndex);
        });

        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === slideIndex);
        });

        currentSlide = slideIndex;
    }

    function nextSlide() {
        const nextIndex = (currentSlide + 1) % modeCards.length;
        goToSlide(nextIndex);
    }

    function startAutoSlide() {
        stopAutoSlide(); // Clear any existing interval
        autoSlideInterval = setInterval(nextSlide, 4000); // Change slide every 4 seconds
    }

    function stopAutoSlide() {
        if (autoSlideInterval) {
            clearInterval(autoSlideInterval);
            autoSlideInterval = null;
        }
    }

    // Add click handlers to dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopAutoSlide();
            goToSlide(index);
            // Restart auto-slide after user interaction
            setTimeout(startAutoSlide, 5000);
        });
    });

    // Add hover handlers to pause auto-slide
    carouselTrack.addEventListener('mouseenter', stopAutoSlide);
    carouselTrack.addEventListener('mouseleave', startAutoSlide);

    // Add touch/swipe support for mobile
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    carouselTrack.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        isDragging = true;
        stopAutoSlide();
    });

    carouselTrack.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        currentX = e.touches[0].clientX;
    });

    carouselTrack.addEventListener('touchend', () => {
        if (!isDragging) return;
        isDragging = false;

        const diffX = startX - currentX;
        const threshold = 50; // Minimum swipe distance

        if (Math.abs(diffX) > threshold) {
            if (diffX > 0) {
                // Swipe left - next slide
                const nextIndex = (currentSlide + 1) % modeCards.length;
                goToSlide(nextIndex);
            } else {
                // Swipe right - previous slide
                const prevIndex = currentSlide === 0 ? modeCards.length - 1 : currentSlide - 1;
                goToSlide(prevIndex);
            }
        }

        // Restart auto-slide after touch interaction
        setTimeout(startAutoSlide, 5000);
    });

    // Initialize first slide and start auto-slide
    goToSlide(0);

    // Start auto-slide after a short delay to ensure everything is loaded
    setTimeout(() => {
        startAutoSlide();
    }, 1000);
}

// Initialize testimonials animations
function initTestimonials() {
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    const scrollContainer = document.querySelector('.testimonials-scroll-container');

    // Animate testimonial cards on scroll
    function animateTestimonialsOnScroll() {
        testimonialCards.forEach(card => {
            if (isInViewport(card)) {
                const delay = parseInt(card.getAttribute('data-delay')) || 0;
                setTimeout(() => {
                    card.classList.add('active');
                }, delay);
            }
        });
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Add smooth scrolling for horizontal scroll
    if (scrollContainer) {
        let isScrolling = false;
        let hasScrolled = false;

        // Hide scroll indicator after first interaction
        function hideScrollIndicator() {
            if (!hasScrolled) {
                hasScrolled = true;
                scrollContainer.style.setProperty('--scroll-indicator-opacity', '0');
            }
        }

        scrollContainer.addEventListener('wheel', (e) => {
            if (Math.abs(e.deltaX) > Math.abs(e.deltaY)) return;

            e.preventDefault();
            hideScrollIndicator();

            if (!isScrolling) {
                isScrolling = true;

                const scrollAmount = e.deltaY > 0 ? 300 : -300;
                scrollContainer.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });

                setTimeout(() => {
                    isScrolling = false;
                }, 100);
            }
        }, { passive: false });

        // Hide indicator on touch/mouse scroll
        scrollContainer.addEventListener('scroll', hideScrollIndicator);
        scrollContainer.addEventListener('touchstart', hideScrollIndicator);
    }

    // Initial check
    animateTestimonialsOnScroll();

    // Check on scroll
    window.addEventListener('scroll', animateTestimonialsOnScroll);
}

// Initialize pricing section animations
function initPricing() {
    const pricingCards = document.querySelectorAll('.pricing-card');
    const pricingButtons = document.querySelectorAll('.plan-button');

    // Animate pricing cards on scroll
    function animatePricingOnScroll() {
        pricingCards.forEach(card => {
            if (isInViewport(card)) {
                const delay = parseInt(card.getAttribute('data-delay')) || 0;
                setTimeout(() => {
                    card.classList.add('active');
                }, delay);
            }
        });
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Add button click handlers
    pricingButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();

            // Add click animation
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);

            // Handle different button types
            const buttonText = button.textContent.toLowerCase();

            if (buttonText.includes('get started')) {
                // Handle student plan signup

                // You can add actual signup logic here
            } else if (buttonText.includes('get quote')) {
                // Handle school license quote request

                // You can add quote request logic here
            } else if (buttonText.includes('contact us')) {
                // Handle enterprise contact

                // You can add contact form logic here
            }
        });
    });

    // Add hover effects for cards
    pricingCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = card.classList.contains('popular-plan')
                ? 'scale(1.02) translateY(-12px)'
                : 'translateY(-12px)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = card.classList.contains('popular-plan')
                ? 'scale(1.02)'
                : '';
        });
    });

    // Initial check
    animatePricingOnScroll();

    // Check on scroll
    window.addEventListener('scroll', animatePricingOnScroll);
}

// Initialize FAQ accordion
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    // Add click event listeners to FAQ questions
    faqItems.forEach((item, index) => {
        const question = item.querySelector('.faq-question');

        // Open first FAQ item by default after a delay
        if (index === 0) {
            setTimeout(() => {
                item.classList.add('expanded');
            }, 1000);
        }

        question.addEventListener('click', () => {
            // Close other open items
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('expanded')) {
                    otherItem.classList.remove('expanded');
                }
            });

            // Toggle current item
            item.classList.toggle('expanded');
        });
    });

    // Animate FAQ items on scroll
    function animateFAQOnScroll() {
        faqItems.forEach(item => {
            if (isInViewport(item)) {
                const delay = parseInt(item.getAttribute('data-delay')) || 0;
                setTimeout(() => {
                    item.classList.add('active');
                }, delay);
            }
        });
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Initial check
    animateFAQOnScroll();

    // Check on scroll
    window.addEventListener('scroll', animateFAQOnScroll);
}

// Initialize categories section
function initCategories() {
    const categoriesData = activeCategoriesSyllabus

    // Group categories by level
    const groupedCategories = categoriesData.reduce((acc, item) => {
        if (!acc[item.level]) {
            acc[item.level] = [];
        }
        acc[item.level].push(item.syllabus);
        return acc;
    }, {});

    const categoriesContainer = document.getElementById('categoriesContent');
    if (!categoriesContainer) return;

    let categoriesHTML = '';

    // Generate HTML for each category group
    Object.keys(groupedCategories).forEach(level => {
        const syllabuses = groupedCategories[level];

        categoriesHTML += `
            <div class="category-group">
                <a href="/sp/gptsir/store?level=${encodeURIComponent(level)}" class="category-title">${level}</a>
                <div class="category-cards">
        `;

        // Show only first 3 cards initially
        const visibleCards = syllabuses.slice(0, 3);
        const hasMore = syllabuses.length > 3;

        visibleCards.forEach(syllabus => {
            const levelUrl = encodeURIComponent(level);
            const syllabusUrl = encodeURIComponent(syllabus);
            const url = `/sp/gptsir/store?level=${levelUrl}&syllabus=${syllabusUrl}`;

            categoriesHTML += `
                <a href="${url}" class="category-card">
                    <h4 class="category-card-title">${syllabus}</h4>
                </a>
            `;
        });

        categoriesHTML += `
                </div>
        `;

        // Add "Explore More" link if there are more than 3 cards
        if (hasMore) {
            const levelUrl = encodeURIComponent(level);
            const exploreUrl = `/sp/store?level=${levelUrl}`;

            categoriesHTML += `
                <div class="explore-more">
                    <a href="${exploreUrl}" class="explore-more-link">Explore More</a>
                </div>
            `;
        }

        categoriesHTML += `
            </div>
        `;
    });

    categoriesContainer.innerHTML = categoriesHTML;

    // Add scroll animations
    animateCategoriesOnScroll();
}

// Animate categories on scroll
function animateCategoriesOnScroll() {
    const categoryGroups = document.querySelectorAll('.category-group');

    function checkCategoriesInView() {
        categoryGroups.forEach((group, index) => {
            if (isInViewport(group)) {
                setTimeout(() => {
                    group.style.opacity = '1';
                    group.style.transform = 'translateY(0)';
                }, index * 200);
            }
        });
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Initial setup
    categoryGroups.forEach(group => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(40px)';
        group.style.transition = 'all 0.6s ease';
    });

    // Check on scroll
    window.addEventListener('scroll', checkCategoriesInView);
    checkCategoriesInView(); // Check initially
}

// Initialize pre-footer section
function initPreFooter() {
    const preFooterSection = document.querySelector('.pre-footer-section');
    const preFooterContent = document.querySelector('.pre-footer-content');
    const preFooterButton = document.querySelector('.pre-footer-button');

    // Animate pre-footer on scroll
    function animatePreFooterOnScroll() {
        if (preFooterSection && isInViewport(preFooterSection)) {
            preFooterSection.classList.add('active');

            // Add staggered animation to content elements
            setTimeout(() => {
                if (preFooterContent) {
                    preFooterContent.style.opacity = '1';
                    preFooterContent.style.transform = 'translateY(0)';
                }
            }, 200);
        }
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Add button click handler
    if (preFooterButton) {
        preFooterButton.addEventListener('click', (e) => {
            e.preventDefault();

            // Add click animation
            preFooterButton.style.transform = 'translateY(-3px) scale(0.98)';
            setTimeout(() => {
                preFooterButton.style.transform = 'translateY(-3px)';
            }, 150);

            window.location.href = preFooterButton.getAttribute('href');
        });
    }

    // Set initial styles for animation
    if (preFooterContent) {
        preFooterContent.style.opacity = '0';
        preFooterContent.style.transform = 'translateY(30px)';
        preFooterContent.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
    }

    // Initial check
    animatePreFooterOnScroll();

    // Check on scroll
    window.addEventListener('scroll', animatePreFooterOnScroll);
}

// Initialize footer section
function initFooter() {
    const footerSections = document.querySelectorAll('.footer-section, .footer-brand');
    const socialLinks = document.querySelectorAll('.social-link');
    const contactLinks = document.querySelectorAll('.contact-link');

    // Animate footer sections on scroll
    function animateFooterOnScroll() {
        footerSections.forEach((section, index) => {
            if (isInViewport(section)) {
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    }

    // Helper function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.9 &&
            rect.bottom >= 0
        );
    }

    // Add hover effects to social links
    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', () => {
            link.style.transform = 'translateY(-2px) scale(1.05)';
        });

        link.addEventListener('mouseleave', () => {
            link.style.transform = 'translateY(-2px)';
        });
    });

    // Add click handlers to contact links
    contactLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            // Add click animation
            link.style.transform = 'scale(0.98)';
            setTimeout(() => {
                link.style.transform = '';
            }, 150);

            // Handle different contact types
            const href = link.getAttribute('href');
            if (href.startsWith('mailto:')) {
            } else if (href.startsWith('tel:')) {
            }
        });
    });

    // Set initial styles for animation
    footerSections.forEach(section => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        section.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
    });

    // Initial check
    animateFooterOnScroll();

    // Check on scroll
    window.addEventListener('scroll', animateFooterOnScroll);
}

// Initialize scroll-based animations
function initScrollAnimations() {
    // Function to check if an element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // Elements to animate on scroll
    const animatedElements = document.querySelectorAll('.mindmap-section, .feature-node, .ai-features-section, .testimonials-section, .pricing-section, .faq-section, .pre-footer-section, .footer');

    // Initial check on page load
    animatedElements.forEach(element => {
        if (isInViewport(element)) {
            element.classList.add('active');
        }
    });

    // Check on scroll
    window.addEventListener('scroll', () => {
        animatedElements.forEach(element => {
            if (isInViewport(element)) {
                element.classList.add('active');
            }
        });
    });
}

// Sticky Header Functionality
function initStickyHeader() {
    const header = document.querySelector('header');
    let lastScrollTop = 0;

    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // Add scrolled class when scrolling down past 50px
        if (scrollTop > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }

        lastScrollTop = scrollTop;
    }

    // Throttle scroll events for better performance
    let ticking = false;
    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(() => {
                handleScroll();
                ticking = false;
            });
            ticking = true;
        }
    });
}
