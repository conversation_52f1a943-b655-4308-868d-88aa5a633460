class ChatApp {
    constructor() {
        this.isToggleActive = true; // Default state is active
        this.isMobile = window.innerWidth <= 1024;
        this.typingWorker = null;
        this.pendingQuestionContent = null; // Store question content for Ask Doubt
        this.isResponseComplete = true; // Track if response is complete
        this.currentHighlightedQuestionId = null; // Track highlighted question for Ask Doubt
        this.chatHistory = []; // Store chat history for API calls

        // Initialize on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.init();
            });
        } else {
            this.init();
        }
    }

    init() {
        this.setDefaultState();
        this.handleResize();
        this.setupChatEventListeners();

        // Listen for window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    setDefaultState() {
        const toggleBtn = document.getElementById('toggle-btn');
        const toggleLabel = document.querySelector('.toggle-label');

        if (toggleBtn) {
            // Set toggle as active by default
            toggleBtn.classList.add('active');
        }

        if (toggleLabel) {
            toggleLabel.classList.add('toggle-label-color');
        }

        // Set initial panel states
        this.updatePanelStates();
    }

    handleResize() {
        this.isMobile = window.innerWidth <= 1024;
        this.updatePanelStates();
    }

    updatePanelStates() {
        const leftPanel = document.getElementById('left-panel');
        const rightPanel = document.getElementById('right-panel');
        const divider = document.getElementById('divider');
        const chatApp = document.getElementById('chat-app');

        if (!leftPanel || !rightPanel || !divider || !chatApp) return;

        // Reset all classes
        leftPanel.className = 'chat-panel left-panel';
        rightPanel.className = 'chat-panel right-panel';
        divider.className = 'divider';
        chatApp.classList.remove('mobile-right-panel-active');

        if (this.isMobile) {
            // Mobile/Tablet behavior
            if (this.isToggleActive) {
                // Show left panel full screen
                leftPanel.classList.add('left-panel-full');
                rightPanel.classList.add('right-panel-hidden');
                divider.classList.add('divider-hidden');
            } else {
                // Show right panel full screen
                chatApp.classList.add('mobile-right-panel-active');
            }
        } else {
            // Desktop behavior
            if (this.isToggleActive) {
                // Left panel takes full width
                leftPanel.classList.add('left-panel-full');
                rightPanel.classList.add('right-panel-hidden');
                divider.classList.add('divider-hidden');
            } else {
                // Split view - both panels visible
                leftPanel.classList.add('left-panel-half');
                rightPanel.classList.add('right-panel-visible');
            }
        }
    }

    /**
     * Setup chat event listeners for send button and enter key
     */
    setupChatEventListeners() {
        const sendButton = document.getElementById('send-button');
        const chatInput = document.querySelector('input[name="chat"]');

        if (sendButton && chatInput) {
            // Send button click event
            sendButton.addEventListener('click', () => {
                this.handleSendMessage();
            });

            // Enter key press event
            chatInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    this.handleSendMessage();
                }
            });
        }
    }

    /**
     * Handle sending a message
     */
    handleSendMessage() {
        const chatInput = document.querySelector('input[name="chat"]');
        const chatMessages = document.getElementById('chat-messages');
        const sendButton = document.getElementById('send-button');

        if (chatInput && chatMessages) {
            const message = chatInput.value.trim();

            // If response is not complete, pause the typing
            if (!this.isResponseComplete) {
                this.pauseShowingAnswer();
                return;
            }

            if (message) {
                // Add user message
                this.addMessage('user', message, chatMessages);

                // Clear input
                chatInput.value = '';

                this.showTypingLoader(chatMessages);

                // Update send button to show spinner and disable it (waiting for API response)
                this.updateSendButtonToSpinner();
                this.isResponseComplete = false;

                // Check if this is an Ask Doubt scenario
                if (this.pendingQuestionContent) {
                    // Handle Ask Doubt with question content
                    this.processAskDoubtMessage(message, chatMessages);
                } else {
                    // Regular chat message - call retrieveData API
                    this.processRegularChatMessage(message, chatMessages);
                }
            }
        }
    }

    /**
     * Reusable method to add messages to chat
     * @param {string} type - Message type ('user' or 'bot')
     * @param {string} content - Message content
     * @param {HTMLElement} target - Target container element
     */
    addMessage(type, content, target) {
        if (!target || !content || !type) {
            console.error('addMessage: Missing required parameters', `Required parameters: ${type}, ${content}, ${target}`);
            return;
        }

        // Validate type
        if (type !== 'user' && type !== 'bot') {
            console.error('addMessage: Invalid type. Must be "user" or "bot"');
            return;
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${type}`;

        // Create message content
        const contentElement = document.createElement('div');
        contentElement.className = 'chat-message-content';

        // For bot messages, don't set content immediately (typewriter will handle it)
        // For user messages, set content immediately
        if (type === 'user') {
            contentElement.textContent = content;
        }

        // Append elements
        messageElement.appendChild(contentElement);
        target.appendChild(messageElement);

        // Scroll to bottom
        target.scrollTop = target.scrollHeight;

        if (type === 'bot') {
            this.typeWriter(content, 0, null, target);
        }
    }

    /**
     * Update send button to show pause icon and enable it
     */
    updateSendButtonToPause() {
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.innerHTML = '<i class="fas fa-pause"></i>';
            sendButton.disabled = false;
        }
    }

    /**
     * Update send button to show send icon and enable it
     */
    updateSendButtonToSend() {
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            sendButton.disabled = false;
        }
    }

    /**
     * Update send button to show spinner and disable it
     */
    updateSendButtonToSpinner() {
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            sendButton.disabled = true;
        }
    }

    /**
     * Pause showing answer - terminates typing worker and completes response
     */
    pauseShowingAnswer() {
        if (this.typingWorker) {
            this.typingWorker.terminate();
            this.typingWorker = null;
        }
        this.isResponseComplete = true;
        this.updateSendButtonToSend();

        // Remove typing loader if present
        this.hideTypingLoader();

        // Remove highlight from Ask Doubt question if present
        if (this.currentHighlightedQuestionId && typeof removeQuestionCardHighlight === 'function') {
            removeQuestionCardHighlight(this.currentHighlightedQuestionId);
            this.currentHighlightedQuestionId = null;
        }

        // Scroll to bottom
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    /**
     * Show typing loader
     */

    showTypingLoader(target){
        // Create the container
        const isTyping = document.createElement('div');
        isTyping.className = 'is-typing';

        ['jump1', 'jump2', 'jump3'].forEach(cls => {
            const div = document.createElement('div');
            div.className = cls;
            isTyping.appendChild(div);
        });
        target.appendChild(isTyping); // Change to your specific container
        target.scrollTop = target.scrollHeight;
    }

    /**
     * Hide typing loader
     */

    hideTypingLoader(){
        const isTyping = document.querySelector('.is-typing');
        if (!isTyping) {
            console.error('Typing loader is not present');
            return;
        }
        isTyping.remove()
    }


    /**
     * Typing effect for bot message
     */

    typeWriter(text, i, callback, targetContainer){
        // Terminate existing worker if any
        if (this.typingWorker) {
            this.typingWorker.terminate();
            this.typingWorker = null;
        }

        // Find the last bot message element to update
        const chatMessages = targetContainer
        if (!chatMessages) {
            console.error('Chat messages container not found');
            return;
        }

        const botMessages = chatMessages.querySelectorAll('.chat-message.bot .chat-message-content');
        const lastBotMessage = botMessages[botMessages.length - 1];

        if (!lastBotMessage) {
            console.error('No bot message found to animate');
            return;
        }

        // Clear the content initially
        lastBotMessage.textContent = '';

        try {
            this.typingWorker = new Worker('/assets/bookGPTScripts/typeWorker.js');

            // Update send button to pause icon when typing starts
            this.updateSendButtonToPause();

            this.typingWorker.postMessage({ text: text, index: i, speed: 10 });

            this.typingWorker.onmessage = (e) => {
                if (e.data.done) {
                    // Animation complete
                    this.typingWorker.terminate();
                    this.typingWorker = null;
                    this.isResponseComplete = true;
                    this.updateSendButtonToSend();

                    // Remove highlight from Ask Doubt question if present
                    if (this.currentHighlightedQuestionId && typeof removeQuestionCardHighlight === 'function') {
                        removeQuestionCardHighlight(this.currentHighlightedQuestionId);
                        this.currentHighlightedQuestionId = null;
                    }

                    if (callback) {
                        callback();
                    }
                    this.handleFormula(text, lastBotMessage)
                } else {
                    // Update the displayed text
                    this.handleFormula(text.substring(0, e.data.index), lastBotMessage)
                }
                // Auto-scroll to bottom
                chatMessages.scrollTop = chatMessages.scrollHeight;
            };

            // Handle worker errors
            this.typingWorker.onerror = (error) => {
                console.error('Worker error:', error);
                this.typingWorker.terminate();
                this.typingWorker = null;
                this.isResponseComplete = true;
                this.updateSendButtonToSend();

                // Remove highlight from Ask Doubt question if present
                if (this.currentHighlightedQuestionId && typeof removeQuestionCardHighlight === 'function') {
                    removeQuestionCardHighlight(this.currentHighlightedQuestionId);
                    this.currentHighlightedQuestionId = null;
                }

                // Fallback: show full text immediately
                lastBotMessage.textContent = text;
                if (callback) {
                    callback();
                }
            };

        } catch (error) {
            console.error('Failed to create worker:', error);
            this.isResponseComplete = true;
            this.updateSendButtonToSend();

            // Remove highlight from Ask Doubt question if present
            if (this.currentHighlightedQuestionId && typeof removeQuestionCardHighlight === 'function') {
                removeQuestionCardHighlight(this.currentHighlightedQuestionId);
                this.currentHighlightedQuestionId = null;
            }

            // Fallback: show full text immediately
            lastBotMessage.textContent = text;
            if (callback) {
                callback();
            }
        }
    }

    /**
     * Show formula using katex
     */
    handleFormula(currentText, lastBotMessage){
        const tempElement = document.createElement("div");
        tempElement.innerHTML = currentText;

        renderMathInElement(tempElement, {
            delimiters: [
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        });
        lastBotMessage.innerHTML = marked.parse(tempElement.innerHTML);
    }

    /**
     * Toggle functionality for buttons
     * @param {string} toggleId - The ID of the toggle button
     * @returns {Function} - Event handler function
     */
    toggle(toggleId) {
        return () => {
            const toggleBtn = document.getElementById(toggleId);
            const toggleLabel = document.querySelector('.toggle-label');

            if (toggleBtn) {
                // Toggle the button's active state
                this.isToggleActive = !this.isToggleActive;

                if (this.isToggleActive) {
                    toggleBtn.classList.add('active');
                    if (toggleLabel) {
                        toggleLabel.classList.add('toggle-label-color');
                    }
                } else {
                    toggleBtn.classList.remove('active');
                    if (toggleLabel) {
                        toggleLabel.classList.remove('toggle-label-color');
                    }
                }

                // Update panel states based on new toggle state
                this.updatePanelStates();
            }
        };
    }


    /**
     * Open toggle functionality
     * @param {string} toggleId - The ID of the toggle button
     * @returns {Function} - Event handler function
     */
    openToggle(toggleId) {
        return () => {
            const toggleBtn = document.getElementById(toggleId);
            const toggleLabel = document.querySelector('.toggle-label');

            if (toggleBtn && !this.isToggleActive) {
                // Set toggle to active state
                this.isToggleActive = true;

                toggleBtn.classList.add('active');
                if (toggleLabel) {
                    toggleLabel.classList.add('toggle-label-color');
                }

                // Update panel states based on new toggle state
                this.updatePanelStates();
            }
        };
    }

    /**
     * Close toggle functionality
     * @param {string} toggleId - The ID of the toggle button
     * @returns {Function} - Event handler function
     */
    closeToggle(toggleId) {
        return () => {
            const toggleBtn = document.getElementById(toggleId);
            const toggleLabel = document.querySelector('.toggle-label');

            if (toggleBtn && this.isToggleActive) {
                // Set toggle to inactive state
                this.isToggleActive = false;

                toggleBtn.classList.remove('active');
                if (toggleLabel) {
                    toggleLabel.classList.remove('toggle-label-color');
                }

                // Update panel states based on new toggle state
                this.updatePanelStates();
            }
        };
    }

    /**
     * Reset chat messages - clears all messages from the chat container
     */
    resetChatMessages() {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.innerHTML = '';
            console.log('Chat messages have been reset');
        }

        // Clear any pending question content
        this.pendingQuestionContent = null;

        // Clear chat history array
        this.chatHistory = [];

        // Reset chat input placeholder
        const chatInput = document.querySelector('input[name="chat"]');
        if (chatInput) {
            chatInput.placeholder = "Type your message...";
        }

        // Hide any Ask Doubt indicator
        this.hideAskDoubtIndicator();
    }

    /**
     * Load chat history for the current chapter
     * @param {string} chapterResId - The resource ID for the chapter
     */
    async loadChatHistory(chapterResId) {
        if (!chapterResId || !username) {
            console.log('Missing required parameters for loading chat history');
            return;
        }

        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) {
            console.error('Chat messages container not found');
            return;
        }

        try {
            // Show loading indicator
            this.showChatHistoryLoader(chatMessages);

            // Construct API URL
            const historyAPI = `/gptLog/find?username=${encodeURIComponent(username)}&resId=${encodeURIComponent(chapterResId)}&pageNo=0`;

            // Fetch chat history
            const response = await fetch(historyAPI);
            const chatHistoryData = await response.json();

            // Hide loading indicator
            this.hideChatHistoryLoader(chatMessages);

            // Process and display chat history
            if (chatHistoryData.GptLogs && chatHistoryData.GptLogs.length > 0) {
                const historyLogs = chatHistoryData.GptLogs.reverse(); // Reverse to show oldest first

                // Clear and populate chat history array for API calls
                this.chatHistory = [];

                // Display each message from history and build chat history array
                historyLogs.forEach(log => {
                    const userMessage = log.userPrompt;
                    const botResponse = log.response;
                    const obj = {
                        ai:"",
                        user:""
                    }
                    if (userMessage) {
                        this.addMessage('user', userMessage, chatMessages);
                        // Add to chat history array
                        obj.user = userMessage
                    }
                    if (botResponse) {
                        this.addHistoryMessage('bot', botResponse, chatMessages);
                        // Add to chat history array
                        obj.ai = botResponse
                    }
                    this.chatHistory.push(obj)
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                });

                console.log(`Loaded ${historyLogs.length} chat history entries`);
                console.log('Chat history array:', this.chatHistory);

                // Scroll to bottom after loading all history messages
                setTimeout(() => {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 100);
            } else {
                console.log('No chat history found for this chapter');
            }

        } catch (error) {
            console.error('Error loading chat history:', error);
            this.hideChatHistoryLoader(chatMessages);
        }
    }

    /**
     * Add a message from history (without typewriter effect)
     * @param {string} type - Message type ('user' or 'bot')
     * @param {string} content - Message content
     * @param {HTMLElement} target - Target container element
     */
    addHistoryMessage(type, content, target) {
        if (!target || !content || !type) {
            console.error('addHistoryMessage: Missing required parameters');
            return;
        }

        // Validate type
        if (type !== 'user' && type !== 'bot') {
            console.error('addHistoryMessage: Invalid type. Must be "user" or "bot"');
            return;
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${type}`;

        // Create message content
        const contentElement = document.createElement('div');
        contentElement.className = 'chat-message-content';

        // Set content immediately (no typewriter effect for history)
        if (type === 'bot') {
            // Handle formulas for bot messages
            const tempElement = document.createElement("div");
            tempElement.innerHTML = content;

            if (typeof renderMathInElement === 'function') {
                renderMathInElement(tempElement, {
                    delimiters: [
                        { left: "\\(", right: "\\)", display: false },
                        { left: "\\[", right: "\\]", display: true }
                    ]
                });
            }

            if (typeof marked !== 'undefined' && marked.parse) {
                contentElement.innerHTML = marked.parse(tempElement.innerHTML);
            } else {
                contentElement.innerHTML = tempElement.innerHTML;
            }
        } else {
            contentElement.textContent = content;
        }

        // Append elements
        messageElement.appendChild(contentElement);
        target.appendChild(messageElement);

        // Scroll to bottom
        target.scrollTop = target.scrollHeight;
    }

    /**
     * Show chat history loading indicator
     * @param {HTMLElement} target - Target container element
     */
    showChatHistoryLoader(target) {
        // Remove any existing loader
        this.hideChatHistoryLoader(target);

        // Create loader element
        const loader = document.createElement('div');
        loader.className = 'chat-history-loader';
        loader.innerHTML = `
            <div style="text-align: center; padding: 20px; color: #666;">
                <i class="fas fa-spinner fa-spin" style="margin-right: 8px;"></i>
                Loading chat history...
            </div>
        `;

        target.appendChild(loader);
        target.scrollTop = target.scrollHeight;
    }

    /**
     * Hide chat history loading indicator
     * @param {HTMLElement} target - Target container element
     */
    hideChatHistoryLoader(target) {
        const loader = target.querySelector('.chat-history-loader');
        if (loader) {
            loader.remove();
        }
    }

    /**
     * Get current chat history array
     * @returns {Array} - Current chat history
     */
    getChatHistory() {
        return this.chatHistory || [];
    }

    /**
     * Add message to chat history array
     * @param {string} role - 'user' or 'assistant'
     * @param {string} content - Message content
     */
    addToChatHistory(role, content) {
        if (!this.chatHistory) {
            this.chatHistory = [];
        }
    }

    /**
     * Initiate Ask Doubt functionality - stores question content and opens chat
     * @param {string} questionContent - The question content to be included in the query
     */
    initiateAskDoubt(questionContent) {
        // Store the question content for later use
        this.pendingQuestionContent = questionContent;

        // Open the chat toggle (close the toggle to show chat panel)
        if (document.getElementById('toggle-btn').classList.contains("active")) {
            this.closeToggle('toggle-btn')();
        }

        // Focus on the chat input and update placeholder
        const chatInput = document.querySelector('input[name="chat"]');
        if (chatInput) {
            chatInput.focus();
            chatInput.placeholder = "Ask your doubt about this question...";
        }

    }

    /**
     * Show visual indicator that Ask Doubt mode is active
     */
    showAskDoubtIndicator() {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            // Remove any existing indicator
            const existingIndicator = chatMessages.querySelector('.ask-doubt-indicator');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            // Add new indicator
            const indicator = document.createElement('div');
            indicator.className = 'ask-doubt-indicator';
            indicator.innerHTML = '<div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 10px; margin: 10px 0; text-align: center; color: #1976d2; font-size: 14px;"><i class="fas fa-question-circle"></i> Ask Doubt mode active - Type your question below</div>';
            chatMessages.appendChild(indicator);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    /**
     * Hide Ask Doubt indicator
     */
    hideAskDoubtIndicator() {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            const indicator = chatMessages.querySelector('.ask-doubt-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
    }

    /**
     * Process regular chat message when user sends input
     * @param {string} userInput - The user's input
     * @param {HTMLElement} chatMessages - Chat messages container
     */
    async processRegularChatMessage(userInput, chatMessages) {

        // Prepare request object for retrieveData API
        const requestObj = {
            namespace: namespace,
            query: userInput,
            resType: 'userInput',
            chatHistory: this.chatHistory,
            resId: resId,
            chapterId: currentChapterId,
            bookId: null // Will be set if available globally
        };

        try {
            console.log('Regular chat API call with history:', requestObj.chatHistory);

            // Use the global retrieveDataHandler function which handles storePdfVectors blocking
            let answer;
            if (typeof window.retrieveDataHandler === 'function') {
                answer = await window.retrieveDataHandler(requestObj, '', userInput);
            } else {
                // Fallback to direct API call if retrieveDataHandler is not available
                const response = await fetch('/prompt/retrieveData', {
                    method: "POST",
                    body: JSON.stringify(requestObj),
                    headers: {
                        "Content-Type": "application/json"
                    }
                });
                const responseData = await response.json();
                answer = responseData.answer;
            }

            // Hide typing loader and show bot response (button will change to pause when typing starts)
            this.hideTypingLoader();

            // Only add message if it's not a pending request message
            if (answer !== "Processing your request, please wait...") {
                this.addMessage('bot', answer, chatMessages);
                const hisObj = {
                    ai: answer,
                    user: userInput
                }
                this.chatHistory.push(hisObj);
            } else {
                // Show a message that the request is being processed
                this.addMessage('bot', 'Your message is being processed. Please wait while we prepare the content...', chatMessages);
            }
        } catch (error) {
            console.error('Error in regular chat API call:', error);
            this.hideTypingLoader();
            this.isResponseComplete = true;
            this.updateSendButtonToSend();

            this.addMessage('bot', 'Sorry, there was an error processing your message. Please try again.', chatMessages);
        }
    }

    /**
     * Process Ask Doubt message when user sends their input
     * @param {string} userInput - The user's input/doubt
     * @param {HTMLElement} chatMessages - Chat messages container
     */
    async processAskDoubtMessage(userInput, chatMessages) {
        // Construct the query with both question content and user input
        const fullQuery = this.pendingQuestionContent + "\n\nUser's doubt: " + userInput;

        // Clear the pending question content
        this.pendingQuestionContent = null;

        // Reset chat input placeholder and hide indicator
        const chatInput = document.querySelector('input[name="chat"]');
        if (chatInput) {
            chatInput.placeholder = "Type your message...";
        }
        this.hideAskDoubtIndicator();

        // Prepare request object for retrieveData API
        const requestObj = {
            namespace: namespace,
            query: fullQuery,
            resType: 'userInput',
            chatHistory: this.chatHistory,
            resId: resId,
            chapterId: currentChapterId,
            bookId: null // Will be set if available globally
        };

        try {
            console.log('Ask Doubt API call with history:', requestObj.chatHistory);

            // Use the global retrieveDataHandler function which handles storePdfVectors blocking
            let answer;
            if (typeof window.retrieveDataHandler === 'function') {
                answer = await window.retrieveDataHandler(requestObj, '', userInput);
            } else {
                // Fallback to direct API call if retrieveDataHandler is not available
                const response = await fetch('/prompt/retrieveData', {
                    method: "POST",
                    body: JSON.stringify(requestObj),
                    headers: {
                        "Content-Type": "application/json"
                    }
                });
                const responseData = await response.json();
                answer = responseData.answer;
            }

            // Hide typing loader and show bot response (button will change to pause when typing starts)
            this.hideTypingLoader();

            // Only add message if it's not a pending request message
            if (answer !== "Processing your request, please wait...") {
                this.addMessage('bot', answer, chatMessages);
                const hisObj = {
                    ai: answer,
                    user: userInput
                }
                this.chatHistory.push(hisObj);
            } else {
                // Show a message that the request is being processed
                this.addMessage('bot', 'Your doubt is being processed. Please wait while we prepare the content...', chatMessages);
            }

        } catch (error) {
            console.error('Error in Ask Doubt API call:', error);
            this.hideTypingLoader();
            this.isResponseComplete = true;
            this.updateSendButtonToSend();

            // Remove highlight from Ask Doubt question if present
            if (this.currentHighlightedQuestionId && typeof removeQuestionCardHighlight === 'function') {
                removeQuestionCardHighlight(this.currentHighlightedQuestionId);
                this.currentHighlightedQuestionId = null;
            }

            this.addMessage('bot', 'Sorry, there was an error processing your doubt. Please try again.', chatMessages);
        }
    }
}