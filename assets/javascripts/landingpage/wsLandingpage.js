// Mobile Menu Toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const navActions = document.querySelector('.nav-actions');
    const loginBtn = document.querySelector('.login-btn');
    const storeBtn = document.querySelector('.store-btn');
    const heroStore = document.querySelector('.hero-store');

    loginBtn.addEventListener('click', ()=>{
        window.location.href = "/books/store?mode=loginform"
    })

    storeBtn.addEventListener('click', ()=>{
        window.location.href = "/books/store"
    })
    heroStore.addEventListener('click', ()=>{
        window.location.href = "/books/store"
    })

    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navActions.classList.toggle('active');
            mobileMenuToggle.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Header scroll effect
    const header = document.querySelector('.header');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            header.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;
    });

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.offering-card, .stakeholder-card, .hero-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });

    // Progress indicators animation in hero card
    const progressItems = document.querySelectorAll('.progress-item');
    let currentProgress = 0;

    setInterval(() => {
        progressItems.forEach(item => item.classList.remove('active'));
        progressItems[currentProgress].classList.add('active');
        currentProgress = (currentProgress + 1) % progressItems.length;
    }, 2000);

    // Button click handlers
    const scheduleButtons = document.querySelectorAll('button:contains("Schedule a Demo"), .btn-primary');
    const storeButtons = document.querySelectorAll('button:contains("Visit Store"), .btn-secondary');

    // Schedule Demo button functionality
    document.addEventListener('click', function(e) {
        if (e.target.textContent.includes('Schedule a Demo') || e.target.textContent.includes('Schedule Consultation')) {
            // You can replace this with actual scheduling functionality
            alert('Demo scheduling functionality would be integrated here. This could open a calendar widget or redirect to a scheduling page.');
        }

        if (e.target.textContent.includes('Visit Store') || e.target.textContent.includes('Store')) {
            // You can replace this with actual store link
            alert('Store functionality would be integrated here. This could redirect to your product store.');
        }

        if (e.target.textContent.includes('Learn More')) {
            // Scroll to relevant section or open modal with more details
            const card = e.target.closest('.offering-card');
            if (card) {
                const cardTitle = card.querySelector('.card-title').textContent;
                alert(`More information about ${cardTitle} would be displayed here. This could open a detailed modal or redirect to a product page.`);
            }
        }
    });

    // Form validation (if forms are added later)
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    // Utility function to add loading state to buttons
    function addLoadingState(button) {
        const originalText = button.textContent;
        button.textContent = 'Loading...';
        button.disabled = true;

        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
        }, 2000);
    }

    // Add hover effects to cards
    const cards = document.querySelectorAll('.offering-card, .stakeholder-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Parallax effect for hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroCard = document.querySelector('.hero-card');

        if (heroCard) {
            const rate = scrolled * -0.5;
            heroCard.style.transform = `translateY(${rate}px) rotate(5deg)`;
        }
    });

    // Add typing effect to hero title (optional enhancement)
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';

        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }

        type();
    }

    // Initialize typing effect for hero title (uncomment if desired)
    // const heroTitle = document.querySelector('.hero-title');
    // if (heroTitle) {
    //     const originalText = heroTitle.textContent;
    //     typeWriter(heroTitle, originalText, 50);
    // }

    // Add scroll-to-top functionality
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '↑';
    scrollToTopBtn.className = 'scroll-to-top';
    scrollToTopBtn.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-size: 20px;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1000;
    `;

    document.body.appendChild(scrollToTopBtn);

    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.style.opacity = '1';
        } else {
            scrollToTopBtn.style.opacity = '0';
        }
    });

    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});

// CSS animations for intersection observer
const style = document.createElement('style');
style.textContent = `
    .offering-card, .stakeholder-card, .hero-card {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.6s ease, transform 0.6s ease;
    }
    
    .offering-card.animate-in, .stakeholder-card.animate-in, .hero-card.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    @media (max-width: 768px) {
        .nav-actions.active {
            display: flex;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            flex-direction: column;
            padding: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .nav-menu.active {
            gap: 1rem;
        }
        
        .nav-actions.active {
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        
        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }
        
        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }
    }
`;
document.head.appendChild(style);
