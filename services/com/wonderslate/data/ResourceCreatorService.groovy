package com.wonderslate.data

import com.wonderslate.DataNotificationService
import com.wonderslate.admin.AdminService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.BatchResourcesDtl
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.NotificationDtl
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.usermanagement.User
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.w3c.dom.Document
import org.w3c.dom.Element
import org.w3c.dom.NamedNodeMap
import org.w3c.dom.Node
import org.w3c.dom.NodeList
import org.xml.sax.SAXException

import javax.xml.parsers.DocumentBuilder
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.parsers.ParserConfigurationException
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.zip.ZipEntry
import java.util.zip.ZipFile
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream

@Transactional
class ResourceCreatorService {
    def springSecurityService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    FolderService folderService
    UtilService utilService
    def grailsApplication
    def redisService
    AdminService adminService
    AsyncLogsService asyncLogsService
    MetainfoService metainfoService
    QuizExtractorService quizExtractorService
    GptLogService gptLogService

    List<String> filesListInDir
    String opfFileName
    Document dom
    def wantedNode = false
    Vector<String> v



    //Quiz creation
    @Secured(['ROLE_USER']) @Transactional

    def quizBulkCreation(params,request,quizId,bookId,chapterId,resId){
        int noOfQuestions = Integer.parseInt(params.noOfQuestions)
        Long previousDirectionId = 0
        String directions
        boolean addedQuiz=false;

        for (int i = 0; i < noOfQuestions; i++) {

            directions = request.getParameter("directions" + i)

            if (directions != null && !"".equals(directions)) {
                String firstPart = directions
                String secondPart = "";
                if (directions.indexOf('(') != -1 && directions.indexOf(')') != -1) {
                    firstPart = directions.substring(0, directions.indexOf('('))
                    secondPart = directions.substring(directions.indexOf(')'))
                    secondPart.replace(')', ':')
                }

                DirectionsMst directionMst = new DirectionsMst(directions: (firstPart + secondPart), passage: false)
                directionMst.save(failOnError: true, flush: true)
                previousDirectionId = directionMst.id
            }
            if (!"on".equals(request.getParameter("direction" + i))) previousDirectionId = null
            Date expiryDate = null;
            if(request.getParameter("expiryDate"+i)!=null&&!"".equals(request.getParameter("expiryDate"+i)))  expiryDate = df.parse(request.getParameter("expiryDate"+i))
            ObjectiveMst om = new ObjectiveMst(quizId: quizId, quizType: params.resourceType, question: quizExtractorService.extractImage(request.getParameter("question" + i),bookId,chapterId,resId),
                    option1: quizExtractorService.extractImage((request.getParameter("option" + i + "_" + 1).length()>254?request.getParameter("option" + i + "_" + 1).substring(0,254):request.getParameter("option" + i + "_" + 1)),bookId,chapterId,resId),
                    option2: quizExtractorService.extractImage((request.getParameter("option" + i + "_" + 2).length()>254?request.getParameter("option" + i + "_" + 2).substring(0,254):request.getParameter("option" + i + "_" + 2)),bookId,chapterId,resId),
                    option3: quizExtractorService.extractImage((request.getParameter("option" + i + "_" + 3).length()>254?request.getParameter("option" + i + "_" + 3).substring(0,254):request.getParameter("option" + i + "_" + 3)),bookId,chapterId,resId),
                    option4: quizExtractorService.extractImage((request.getParameter("option" + i + "_" + 4).length()>254?request.getParameter("option" + i + "_" + 4).substring(0,254):request.getParameter("option" + i + "_" + 4)),bookId,chapterId,resId),
                    option5: quizExtractorService.extractImage(((request.getParameter("option" + i + "_" + 5)!=null&&request.getParameter("option" + i + "_" + 5).length()>254)?request.getParameter("option" + i + "_" + 5).substring(0,254):request.getParameter("option" + i + "_" + 5)),bookId,chapterId,resId),
                    answer1: request.getParameter("answer" + i + "_" + 1), answer2: request.getParameter("answer" + i + "_" + 2), answer3: request.getParameter("answer" + i + "_" + 3), answer4: request.getParameter("answer" + i + "_" + 4), answer5: request.getParameter("answer" + i + "_" + 5),
                    answerDescription: quizExtractorService.extractImage(request.getParameter("answerDescription"+i),bookId,chapterId,resId), hint: params.hint, examYear: params.examYear, difficultylevel: params.difficultylevel,
                    topicId: (params.subtopicid != null && !"".equals(params.subtopicid)) ? new Integer(params.subtopicid) : null, directions: request.getParameter("directions" + i), directionId: previousDirectionId,
                    answer: params.answer,subject:request.getParameter("subject" + i),expiryDate: expiryDate,marks:request.getParameter("marks"+i) !=null&&!"".equals(request.getParameter("marks"+i))?new Double(request.getParameter("marks"+i)):null,
                    negativeMarks: request.getParameter("negativeMarks"+i) !=null&&!"".equals(request.getParameter("negativeMarks"+i))?new Integer(request.getParameter("negativeMarks"+i)):null,
                    explainLink: params.explainLink, startTime: params.startTime, endTime: params.endTime
            )

            om.save(failOnError: true, flush: true)
            addedQuiz=true;
        }
        return addedQuiz
    }
    def quizCreation(params,request,session){
       Integer siteId =utilService.getSiteId(request,session);
        def quizId, resourceDtlId, objectiveMstId;
        boolean passageQuiz = false;
        boolean addedQuiz=false;
        boolean testSeriesQuiz=false;
        if (params.passage != null && !"".equals(params.passage)) passageQuiz = true;

            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()
            def resourceDtlInstance

            if(session.getAttribute("htmlId")!=null)
                resourceDtlInstance = ResourceDtl.findById(new Integer(session.getAttribute("htmlId")+""))
            else
                resourceDtlInstance = new ResourceDtl()

            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
        if("notes".equals(params.page)||"QA".equals(params.resourceType)||"Short QA".equals(params.resourceType)) {
            if(params.chapterId!=null&&!"".equals(params.chapterId)) resourceDtlInstance.chapterId = new Integer(params.chapterId)
        }else{
            resourceDtlInstance.quizMode = params.page
        }
            resourceDtlInstance.resourceName = params.resourceName!=null?params.resourceName:"Standard name"
            resourceDtlInstance.examSyllabus = params.examSyllabus
            resourceDtlInstance.grade = params.grade
            resourceDtlInstance.language1 = params.language1
            resourceDtlInstance.language2 = params.language2
            resourceDtlInstance.examSubject = params.examSubject
            resourceDtlInstance.siteId = utilService.getSiteId(request,session)
            resourceDtlInstance.resSubType = params.resSubType
            resourceDtlInstance.currentAffairsType = params.currentAffairsType
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if(!user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
            }){
                resourceDtlInstance.sharing="createdbyuser"
            }
            if(params.testStartDate!=null&&!"".equals(params.testStartDate)&&!" ".equals(params.testStartDate)) {
                testSeriesQuiz=true
                DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date columnValue = df1.parse(params.testStartDate);
                columnValue = utilService.convertDate(columnValue,"IST","UTC")
                resourceDtlInstance.testStartDate = columnValue
            }
            if(params.testEndDate!=null&&!"".equals(params.testEndDate)&&!" ".equals(params.testEndDate)) {
                DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date columnValue = df1.parse(params.testEndDate);
                columnValue = utilService.convertDate(columnValue,"IST","UTC")
                resourceDtlInstance.testEndDate = columnValue
            }
            if(params.testResultDate!=null&&!"".equals(params.testResultDate)&&!" ".equals(params.testResultDate)) {
                DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date columnValue = df1.parse(params.testResultDate);
                columnValue = utilService.convertDate(columnValue,"IST","UTC")
                resourceDtlInstance.testResultDate = columnValue
            }
            if(params.examId!=null) resourceDtlInstance.examId = (params.examId!=null)?new Integer(params.examId):null

            if (passageQuiz) {
                resourceDtlInstance.quizMode = "passage";
                resourceDtlInstance.chapterDesc = params.passage;
            }
            resourceDtlInstance.allowReAttempt = params.reAttemptValue!=null?params.reAttemptValue:null
            resourceDtlInstance.save(failOnError: true, flush: true)
            if("Current Affairs".equals(resourceDtlInstance.resSubType)){
                String pattern = "dd-MM-yyyy";
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

                String inputDate = simpleDateFormat.format(resourceDtlInstance.dateCreated);

                adminService.getCurrentAffairsLatestAndStartDates(resourceDtlInstance.currentAffairsType)
                adminService.getCurrentAffairsQuizId(inputDate,resourceDtlInstance.currentAffairsType)
                try {
                    List sites = SiteMst.findAllByCurrentAffairsType(resourceDtlInstance.currentAffairsType)
                    sites.each { site ->
                        NotificationDtl notificationDtl = new NotificationDtl(title: "Today's current affairs game is available now.", body: "Today's current affairs game is available. Play and Practice", createdBy: "System",
                                siteId: site.id, link: "", sendTo: "all", messageType: "gameupdated", resId: null)
                        notificationDtl.save(flush: true, failOnError: true);
                        dataNotificationService.sendNotificationToAll(notificationDtl)
                    }
                }catch(Exception e){
                    println("Exception sending notifications")
                }

            }
            ChaptersMst chaptersMst = null
            BooksMst booksMst = null

        String chapterId="",bookId="",resId=""+resourceDtlInstance.id

        if (resourceDtlInstance.sharing == null&&resourceDtlInstance.chapterId!=null) {
            dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
            chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
            booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            chapterId = ""+chaptersMst.id
            bookId = ""+chaptersMst.bookId
        }
            if("notes".equals(params.page)||"QA".equals(params.resourceType)||"Short QA".equals(params.resourceType)) {
                    if (resourceDtlInstance.sharing == null) {
                        dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                    if(chaptersMst!=null)    dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
                        if (booksMst!=null&&"published".equals(booksMst.status)) {
                            String siteIdList = siteId.toString();

                            if (siteId.intValue() == 1) {
                                if (redisService.("siteIdList_" + siteId) == null) {
                                    dataProviderService.getSiteIdList(siteId)
                                }

                                siteIdList = redisService.("siteIdList_" + siteId)
                            }
                            dataProviderService.getLatestQuizzes(siteIdList, siteId);
                        }

                    } else {
                        dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
                    }
            }else if(user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
            }){
                redisService.("getindependentResourcDetails"+params.page+"_"+siteId) = null
                redisService.("getindependentResourcDetails"+params.page+"_"+siteId+"_recordCount") = null
                dataNotificationService.independentResourceUpdated(resourceDtlInstance.id, siteId)
            }else{
                dataProviderService.getUserResources("Multiple Choice Questions",springSecurityService.currentUser.username)
                dataProviderService.getUserResources("all",springSecurityService.currentUser.username)
            }
            quizId = quizIdGenerator.id
            resourceDtlId = resourceDtlInstance.id

            if ("true".equals(params.bulkcreation)) {
              addedQuiz = quizBulkCreation(params,request,quizId,bookId,chapterId,resId)
            } else {
                Date expiryDate = null;
                if(request.getParameter("expiryDate")!=null&&!"".equals(request.getParameter("expiryDate")))  expiryDate = df.parse(request.getParameter("expiryDate"))

                ObjectiveMst om = new ObjectiveMst(quizId: quizId, quizType: params.resourceType, question: quizExtractorService.extractImage(params.question,bookId,chapterId,resId),
                        option1: quizExtractorService.extractImage(params.option1,bookId,chapterId,resId), option2: quizExtractorService.extractImage(params.option2,bookId,chapterId,resId),
                        option3: quizExtractorService.extractImage(params.option3,bookId,chapterId,resId),
                        option4: quizExtractorService.extractImage(params.option4,bookId,chapterId,resId), option5: quizExtractorService.extractImage(params.option5,bookId,chapterId,resId),
                        answer1: params.answer1, answer2: params.answer2, answer3: params.answer3, answer4: params.answer4, answer5: params.answer5,
                        answerDescription: quizExtractorService.extractImage(params.answerDescription,bookId,chapterId,resId), hint: params.hint, examYear: params.examYear, difficultylevel: params.difficultylevel,
                        topicId: (params.subtopicid != null && !"".equals(params.subtopicid)) ? new Integer(params.subtopicid) : null,subject: params.subject,
                        expiryDate: expiryDate,marks:request.getParameter("marks")!=null&&!"".equals(params.marks)?new Double(request.getParameter("marks")):null ,
                        negativeMarks:request.getParameter("negativeMarks")!=null&&!"".equals(params.negativeMarks)?new Integer(request.getParameter("negativeMarks")):null,
                        explainLink: params.explainLink, startTime: params.startTime, endTime: params.endTime, isValidAnswerKey: params.isValidAnswerKey)

                om.save(failOnError: true, flush: true)
                om.answer = params.answer
                if (params.directions != null && !"".equals(params.directions)) {
                    DirectionsMst  directionsMst = new DirectionsMst(directions: quizExtractorService.extractImage(params.directions,bookId,chapterId,resId))
                    directionsMst.save(failOnError: true, flush: true)
                     om.directions = params.directions
                    om.directionId = directionsMst.id
                }
                om.save(failOnError: true , flush: true)
                objectiveMstId = om.id
                addedQuiz=true;
            }
        if(booksMst!=null&&!"true".equals(booksMst.hasQuiz)){
            booksMst.hasQuiz="true"

            booksMst.save(failOnError: true, flush: true)
            BooksMst.wsuser.executeUpdate("update BooksMst set hasQuiz='true' where id="+booksMst.id)
            BooksMst.wsshop.executeUpdate("update BooksMst set hasQuiz='true' where id="+booksMst.id)
        }
        if(params.folderId!=null&&!"".equals(params.folderId)) folderService.addResourceToFolder(params.folderId,""+resourceDtlId)

        //if the book as ai counterpart add this to that also.
        if("Multiple Choice Questions".equals(resourceDtlInstance.resType)&&resourceDtlInstance.chapterId!=null){
            println("**** vendor is "+booksMst.vendor)
            if(booksMst!=null&&booksMst.vendor!=null){
                //check the value in booksMst.vendor is number
                try {
                    Long aiBookId = new Long(""+booksMst.vendor)
                    ChaptersMst chaptersMst1 = new ChaptersMst(name: chaptersMst.name, bookId: aiBookId,sortOrder:chaptersMst.sortOrder )
                    chaptersMst1.save(failOnError: true,flush: true)
                    def readingMaterial = new ResourceDtl(chapterId: chaptersMst1.id, resType: "Notes",filename: "dummy.pdf",resLink: "supload/dummy.pdf",resourceName: chaptersMst1.name,
                            createdBy: springSecurityService.currentUser.username,siteId: booksMst.siteId)
                    readingMaterial.save(flush:true,failOnError:true)

                    //create another instance of resourceDtl
                    ResourceDtl resourceDtlInstance1 = new ResourceDtl(resLink: resourceDtlInstance.resLink, createdBy: resourceDtlInstance.createdBy,
                            resType: resourceDtlInstance.resType, chapterId: chaptersMst1.id, resourceName: resourceDtlInstance.resourceName,
                            siteId: resourceDtlInstance.siteId, gptResourceType: "mcq",
                            vectorStored: resourceDtlInstance.vectorStored)
                    resourceDtlInstance1.save(failOnError: true,flush: true)
                    dataProviderService.getChaptersList(aiBookId)
                    def gptDefaultCreateLog = new GptDefaultCreateLog(readingMaterialResId: readingMaterial.id,promptType: "mcq",promptLabel: "Create MCQs (Multiple Choice Questions)",
                            prompt: "MCQ",response: "MCQ",username: springSecurityService.currentUser.username,resId: resourceDtlInstance1.id)
                    gptDefaultCreateLog.save(flush:true,failOnError:true)

                } catch (Exception e) {
                    //dont do anything
                }
            }
        }
        HashMap returnValues = new HashMap()
        returnValues.put("addedQuiz",addedQuiz)
        returnValues.put("objectiveMstId",objectiveMstId)
        returnValues.put("resourceDtlId",resourceDtlId)
        returnValues.put("testSeriesQuiz",testSeriesQuiz)
        returnValues.put("quizId",quizId)
        return returnValues
    }
    def quizItemEdit(params,request,session){
        println("*** edit thingy started")
        Integer siteId =utilService.getSiteId(request,session);
        def quizId, resourceDtlId, objectiveMstId;
        boolean passageQuiz = false;
        boolean addedQuiz=false;
        boolean testSeriesQuiz=false;
        if (params.passage != null && !"".equals(params.passage)) passageQuiz = true;
        if(params.objectiveMstId!=null&&params.question&&(params.option1&&params.option2)||params.answer) {
            ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.resourceDtlId))
            String chapterId="",bookId="",resId=""+resourceDtl.id
            if(resourceDtl.chapterId!=null){
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                chapterId = ""+chaptersMst.id
                bookId = ""+chaptersMst.bookId
            }
            ObjectiveMst objectMst = ObjectiveMst.findById(new Integer(params.objectiveMstId));
            objectMst.question = quizExtractorService.extractImage(params.question,bookId,chapterId,resId)
            objectMst.option1 = quizExtractorService.extractImage(params.option1,bookId,chapterId,resId)
            objectMst.option2 = quizExtractorService.extractImage(params.option2,bookId,chapterId,resId)
            objectMst.option3 = quizExtractorService.extractImage(params.option3,bookId,chapterId,resId)
            objectMst.option4 = quizExtractorService.extractImage(params.option4,bookId,chapterId,resId)
            objectMst.option5 = quizExtractorService.extractImage(params.option5,bookId,chapterId,resId)
            objectMst.answer1 = params.answer1
            objectMst.answer2 = params.answer2
            objectMst.answer3 = params.answer3
            objectMst.answer4 = params.answer4
            objectMst.answer5 = params.answer5
            if(params.qType!=null) objectMst.qType = params.qType
            if((params.marks != null && !"".equals(params.marks))) objectMst.marks = new Double(params.marks)
            objectMst.answerDescription = quizExtractorService.extractImage(params.answerDescription,bookId,chapterId,resId)
            objectMst.isValidAnswerKey = params.isValidAnswerKey
            if(!"true".equals(params.quickEdit)) {
                 objectMst.hint = params.hint
                objectMst.examYear = params.examYear
                objectMst.difficultylevel = params.difficultylevel
                objectMst.topicId = (params.subtopicid != null && !"".equals(params.subtopicid)) ? new Integer(params.subtopicid) : null
                objectMst.answer = params.answer
                objectMst.subject = params.subject
                objectMst.marks = (params.marks != null && !"".equals(params.marks)) ? new Double(params.marks) : null
                objectMst.negativeMarks = (params.negativeMarks != null && !"".equals(params.negativeMarks)) ? new Double(params.negativeMarks) : null
                objectMst.explainLink = params.explainLink
                objectMst.startTime = params.startTime
                objectMst.endTime = params.endTime


                if (request.getParameter("expiryDate") != null && !"".equals(params.expiryDate)) objectMst.expiryDate = df.parse(request.getParameter("expiryDate")) else objectMst.expiryDate = null
                if (params.marks != null && !"".equals(params.marks)) objectMst.marks = new Double(params.marks)
                //for directions saving. First check if it is a new one or old
                if (params.directions != null && !"".equals(params.directions)) {
                    DirectionsMst directionsMst
                    //update if exists or create new
                    if (objectMst.directions != null && !"".equals("" + objectMst.directions) && objectMst.directionId != null) {
                        directionsMst = DirectionsMst.findById(objectMst.directionId)

                    } else {
                        directionsMst = new DirectionsMst(directions: quizExtractorService.extractImage(params.directions, bookId, chapterId, resId))

                    }
                    directionsMst.directions = params.directions
                    directionsMst.save(failOnError: true, flush: true)
                    objectMst.directions = params.directions
                    objectMst.directionId = directionsMst.id
                } else if ("Yes".equals(params.direction)) {
                    //if the previous direct is applied.
                    String sql = "select directionId from ObjectiveMst where id" +
                            " = (SELECT max(id) FROM ObjectiveMst om where quizId=" + objectMst.quizId + " and om.id<" + objectMst.id + " and directionId is not null)";
                    List results = ObjectiveMst.executeQuery(sql)

                    if (results != null && results.size() > 0)
                        objectMst.directionId = results[0]


                } else {
                    objectMst.directionId = null
                    objectMst.directions = null
                }
            }
            if("true".equals(params.quickEdit)){
                if (params.directions != null && !"".equals(params.directions)) {
                    DirectionsMst directionsMst
                    //update if exists or create new
                    if (objectMst.directions != null && !"".equals("" + objectMst.directions) && objectMst.directionId != null) {
                        directionsMst = DirectionsMst.findById(objectMst.directionId)

                    } else {
                        directionsMst = new DirectionsMst(directions: quizExtractorService.extractImage(params.directions, bookId, chapterId, resId))

                    }
                    directionsMst.directions = params.directions
                    directionsMst.save(failOnError: true, flush: true)
                    objectMst.directions = params.directions
                    objectMst.directionId = directionsMst.id
                }
                objectMst.marks = (params.marks != null && !"".equals(params.marks)) ? new Double(params.marks) : null
                objectMst.negativeMarks = (params.negativeMarks != null && !"".equals(params.negativeMarks)) ? new Double(params.negativeMarks) : null
            }
            objectMst.save(failOnError: true, flush: true);

            if(!"true".equals(params.quickEdit)) {
                if (passageQuiz) {
                    resourceDtl.quizMode = "passage";
                    resourceDtl.chapterDesc = params.passage;
                }
                resourceDtl.resourceName = params.resourceName
                resourceDtl.allowReAttempt = params.reAttemptValue
                resourceDtl.examSyllabus = params.examSyllabus
                resourceDtl.grade = params.grade
                resourceDtl.language1 = params.language1
                resourceDtl.language2 = params.language2
                resourceDtl.examSubject = params.examSubject
                if (params.testStartDate != null && !"".equals(params.testStartDate) && !" ".equals(params.testStartDate)) {
                    testSeriesQuiz = true;
                    DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    Date columnValue = df1.parse(params.testStartDate);
                    columnValue = utilService.convertDate(columnValue, "IST", "UTC")
                    resourceDtl.testStartDate = columnValue
                }
                if (params.testEndDate != null && !"".equals(params.testEndDate) && !" ".equals(params.testEndDate)) {
                    DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    Date columnValue = df1.parse(params.testEndDate);
                    columnValue = utilService.convertDate(columnValue, "IST", "UTC")
                    resourceDtl.testEndDate = columnValue
                }
                if (params.testResultDate != null && !"".equals(params.testResultDate) && !" ".equals(params.testResultDate)) {
                    DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    Date columnValue = df1.parse(params.testResultDate);
                    columnValue = utilService.convertDate(columnValue, "IST", "UTC")
                    resourceDtl.testResultDate = columnValue
                }

                if(params.examId!=null) resourceDtl.examId = (params.examId != null) ? new Integer(params.examId) : null

                resourceDtl.save(failOnError: true, flush: true)
            }
            resourceDtlId = params.resourceDtlId
            quizId = params.quizId
            objectiveMstId = params.objectiveMstId
            addedQuiz = true;

        } else if(params.objectiveMstId!=null&&params.question&&params.answer) { //if question and answers
            ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.resourceDtlId))
            resourceDtl.resourceName = params.resourceName
            ObjectiveMst objectMst = ObjectiveMst.findById(new Integer(params.objectiveMstId));
            objectMst.question = params.question
            objectMst.difficultylevel = params.difficultylevel
            objectMst.topicId = (params.subtopicid != null && !"".equals(params.subtopicid)) ? new Integer(params.subtopicid) : null
            objectMst.answer = params.answer
            objectMst.qType = params.qType
            objectMst.save(failOnError: true, flush: true);
            resourceDtl.save(failOnError: true, flush: true)
            resourceDtlId = params.resourceDtlId
            quizId = params.quizId
            objectiveMstId = params.objectiveMstId
            addedQuiz = true;
        }
        if("quiz".equals(params.page)) {
            redisService.("getindependentResourcDetails"+params.page+"_"+siteId) = null
            redisService.("getindependentResourcDetails"+params.page+"_"+siteId+"_recordCount") = null
            dataNotificationService.independentResourceUpdated(resourceDtlId, siteId)
        }
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if(!user.authorities.any {
            it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
        }){
            dataProviderService.getUserResources("Multiple Choice Questions",springSecurityService.currentUser.username)
            dataProviderService.getUserResources("all",springSecurityService.currentUser.username)
        }
        HashMap returnValues = new HashMap()
        returnValues.put("addedQuiz",addedQuiz)
        returnValues.put("objectiveMstId",objectiveMstId)
        returnValues.put("resourceDtlId",resourceDtlId)
        returnValues.put("testSeriesQuiz",testSeriesQuiz)
        returnValues.put("quizId",quizId)
        return returnValues
    }

    def quizItemAdd(params,request,session){
        Integer siteId =utilService.getSiteId(request,session);
        Date expiryDate = null;
        def quizId, resourceDtlId, objectiveMstId;
        boolean passageQuiz = false;
        boolean addedQuiz=false;
        boolean testSeriesQuiz=false;
        if (params.passage != null && !"".equals(params.passage)) passageQuiz = true;
        if(request.getParameter("expiryDate")!=null&&!"".equals(request.getParameter("expiryDate")))  expiryDate = df.parse(request.getParameter("expiryDate"))
        ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.resourceDtlId))
        String chapterId="",bookId="",resId=""+resourceDtl.id
        if(resourceDtl.chapterId!=null){
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            chapterId = ""+chaptersMst.id
            bookId = ""+chaptersMst.bookId
        }
        ObjectiveMst om = new ObjectiveMst(quizId: params.quizId, quizType: params.resourceType, question: quizExtractorService.extractImage(params.question,bookId,chapterId,resId),
                option1: quizExtractorService.extractImage(params.option1,bookId,chapterId,resId), option2: quizExtractorService.extractImage(params.option2,bookId,chapterId,resId),
                option3: quizExtractorService.extractImage(params.option3,bookId,chapterId,resId),
                option4: quizExtractorService.extractImage(params.option4,bookId,chapterId,resId), option5: quizExtractorService.extractImage(params.option5,bookId,chapterId,resId),
                answer1: params.answer1, answer2: params.answer2, answer3: params.answer3, answer4: params.answer4, answer5: params.answer5,
                answerDescription: quizExtractorService.extractImage(params.answerDescription,bookId,chapterId,resId), hint: params.hint, examYear: params.examYear, difficultylevel: params.difficultylevel,
                topicId: (params.subtopicid != null && !"".equals(params.subtopicid)) ? new Integer(params.subtopicid) : null, subject: params.subject,
                expiryDate: expiryDate,marks:params.marks!=null&&!"".equals(params.marks)?new Double(params.marks):null ,
                negativeMarks:params.negativeMarks!=null&&!"".equals(params.negativeMarks)?new Integer(params.negativeMarks):null,
                explainLink: params.explainLink, startTime: params.startTime, endTime: params.endTime, isValidAnswerKey: params.isValidAnswerKey,
                qType: params.qType)
        om.save(failOnError: true , flush: true)
        om.answer = params.answer
        om.save(failOnError: true , flush: true)
        if(params.directions!=null&&!"".equals(params.directions)){
            DirectionsMst directionsMst
            //update if exists or create new
            if(om.directions!=null&&!"".equals(""+om.directions)&&om.directionId!=null){
                directionsMst = DirectionsMst.findById(om.directionId)

            }else{
                directionsMst = new DirectionsMst(directions: quizExtractorService.extractImage(params.directions,bookId,chapterId,resId))

            }
            directionsMst.directions = params.directions
            directionsMst.save(failOnError: true,flush: true)
            om.directions = params.directions
            om.directionId = directionsMst.id
        }else if("Yes".equals(params.direction)){
            //if the previous direct is applied.
            String sql="select directionId from ObjectiveMst where id" +
                    " = (SELECT max(id) FROM ObjectiveMst om where quizId="+om.quizId+" and om.id<"+om.id+" and directionId is not null)";
            List results = ObjectiveMst.executeQuery(sql)

            if(results!=null&&results.size()>0)
                om.directionId = results[0]


        }else{
            om.directionId=null

        }
        om.save(failOnError: true , flush: true)
        objectiveMstId = om.id
        resourceDtlId = params.resourceDtlId
        quizId = params.quizId
        addedQuiz=true;
        if("quiz".equals(params.page)) {
            redisService.("getindependentResourcDetails"+params.page+"_"+siteId) = null
            redisService.("getindependentResourcDetails"+params.page+"_"+siteId+"_recordCount") = null
            dataNotificationService.independentResourceUpdated(resourceDtlId, siteId)
        }
        HashMap returnValues = new HashMap()
        returnValues.put("addedQuiz",addedQuiz)
        returnValues.put("objectiveMstId",objectiveMstId)
        returnValues.put("resourceDtlId",resourceDtlId)
        returnValues.put("testSeriesQuiz",testSeriesQuiz)
        returnValues.put("quizId",quizId)
        return returnValues
    }


    def addQuiz(params,request,session) {
        println("*** entered add quiz")
        def quizId, resourceDtlId, objectiveMstId;
        boolean passageQuiz = false;
        boolean addedQuiz=false;
        boolean testSeriesQuiz=false;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        HashMap returnValues = null
        String uploadParentDir="supload"
        ChaptersMst chaptersMst = null
        if(params.chapterId!=null&&!"".equals(params.chapterId)){
             chaptersMst = dataProviderService.getChaptersMst(new Integer(params.chapterId))
            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            if(!"Yes".equals(booksMst.newStorage)) uploadParentDir = "upload"
        }
        if (params.passage != null && !"".equals(params.passage)) passageQuiz = true;
        println("*** entered add quiz 2 and mode is "+params.mode)
        if ("create".equals(params.mode)) {
           returnValues = quizCreation(params,request,session)
        } else if ("edit".equals(params.mode)) {
            println("*** entered add quiz 3")
            returnValues = quizItemEdit(params,request,session)
        } else if ("add".equals(params.mode)) {
            returnValues = quizItemAdd(params,request,session)
        }
            objectiveMstId = returnValues.get("objectiveMstId")
            addedQuiz =     returnValues.get("addedQuiz")
            resourceDtlId =      returnValues.get("resourceDtlId")
            testSeriesQuiz =   returnValues.get("testSeriesQuiz")

            def file = request.getFile('file')

        if (file != null && !file.empty) {
            def filename = file.originalFilename
            def resLink = uploadParentDir+"/quiz/" + objectiveMstId + "/" + filename
            File uploadDir = new File(uploadParentDir+"/quiz/" + objectiveMstId)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+resLink))

            String sql = "update ObjectiveMst set questionFilename='" + filename + "', questionFilepath='" + resLink + "' where id=" + objectiveMstId;
            ObjectiveMst.executeUpdate(sql);
        }
        if(addedQuiz&&"notes".equals(params.page)&&chaptersMst!=null) {
            if(chaptersMst!=null&&!testSeriesQuiz) redisService.("quizPresent_"+chaptersMst.bookId)="Present";
            if(chaptersMst!=null) {
                // we have to optimise this part.
                returnValues.put("chapterMst",chaptersMst)
                redisService.deleteKeysWithPattern("defaultChapterDetail_"+chaptersMst.id)
                redisService.deleteKeysWithPattern("defaultChapterDetailOptimised_"+chaptersMst.bookId)
                metainfoService.getAllChaptersMetaInfo(chaptersMst.bookId)
                metainfoService.quizQuestionAnswers(resourceDtlId)
                if("true".equals(params.finished)) {
                    asyncLogsService.sendMcqUpdateNotification(chaptersMst.id, chaptersMst.bookId, resourceDtlId)
                }

            }

        }
        //dataProviderService.getLiveTests(utilService.getSiteId(request,session))
        String resIdTemp = ""+resourceDtlId
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(resIdTemp))
        println("*** total time")
        println(params.mcqTotalTime)
        resourceDtl.mcqTotalTime = params.mcqTotalTime
        resourceDtl.save(failOnError: true, flush: true)
        dataProviderService.quizDetails(resourceDtl)

        if(springSecurityService.currentUser!=null) {
            String username = springSecurityService.currentUser.username
            dataProviderService.getUserResources('all', username)
            dataProviderService.getUserResources('Multiple Choice Questions', username)
        }
        return returnValues
    }

    def copyToExistingBook(String sourceBookId,String destBooksId){
        BooksMst sourceBooksMst = dataProviderService.getBooksMst(new Long(sourceBookId))
        BooksMst destBooksMst = dataProviderService.getBooksMst(new Long(destBooksId))
        //delete the chapters first
        List chapters = ChaptersMst.findAllByBookId(destBooksMst.id)
        chapters.each {chapter ->
            chapter.bookId = new Long(destBooksMst.id.intValue() * -1)
            chapter.save(failOnError: true, flush: true)
        }

        List sourceChapters = ChaptersMst.findAllByBookId(sourceBooksMst.id,[sort: "sortOrder", order: "asc"])

        sourceChapters.each {chapter ->
            copyChapter(""+chapter.id,""+destBooksMst.id,destBooksMst.siteId)
        }

        copyGptContents(""+destBooksMst.id)
        dataProviderService.getBooksListForUser()
        dataProviderService.refreshCacheForPublishUnpublish(""+destBooksMst.id,destBooksMst.siteId)
        dataProviderService.getWSUnpublishedMyBooks()
        dataProviderService.getBooksListForPubDeskForPublisher(destBooksMst.publisherId)
        return destBooksMst.id

    }



    def copyBook(String sourceBookId){
        BooksMst sourceBooksMst = dataProviderService.getBooksMst(new Long(sourceBookId))
        BookIdGenerator bookIdGenerator = new BookIdGenerator()
        bookIdGenerator.save(failOnError: true, flush: true)
        BooksMst destBooksMst = new BooksMst()
        destBooksMst.id=bookIdGenerator.id
        destBooksMst.publisherId = sourceBooksMst.publisherId
        destBooksMst.description = sourceBooksMst.description
        destBooksMst.title = sourceBooksMst.title + "(Copy)"
        destBooksMst.createdBy = springSecurityService.currentUser.username
        destBooksMst.siteId = sourceBooksMst.siteId
        destBooksMst.price = sourceBooksMst.price
        destBooksMst.status = null
        destBooksMst.packageBookIds = sourceBooksMst.packageBookIds
        destBooksMst.authors = sourceBooksMst.authors
        destBooksMst.bookType = sourceBooksMst.bookType
        destBooksMst.coverImage = sourceBooksMst.coverImage
        destBooksMst.isbn = sourceBooksMst.isbn
        destBooksMst.language = sourceBooksMst.language
        destBooksMst.listprice = sourceBooksMst.listprice
        destBooksMst.showInLibrary = sourceBooksMst.showInLibrary
        destBooksMst.parentBookId = sourceBooksMst.id
        destBooksMst.validityDays = sourceBooksMst.validityDays
        destBooksMst.testsPrice = sourceBooksMst.testsPrice
        destBooksMst.upgradePrice = sourceBooksMst.upgradePrice
        destBooksMst.testsListprice = sourceBooksMst.testsListprice

        BooksMst booksMst1 = destBooksMst.clone()
        BooksMst booksMst2 = destBooksMst.clone()
        destBooksMst.save(failOnError: true, flush: true)
        booksMst1.wsuser.save(failOnError: true,flush: true)
        booksMst2.wsshop.save(failOnError: true,flush: true)
        BooksPermission booksPermission = new BooksPermission(bookId: destBooksMst.id, username: springSecurityService.currentUser.username)
        booksPermission.save(failOnError: true, flush: true)
        destBooksMst.refresh()
        dataProviderService.getBooksListForUser()
        dataProviderService.refreshCacheForPublishUnpublish(""+destBooksMst.id,destBooksMst.siteId)
        dataProviderService.getWSUnpublishedMyBooks()
        dataProviderService.getBooksListForPubDeskForPublisher(destBooksMst.publisherId)

        //now copy chapters
        List chapters = ChaptersMst.findAllByBookId(sourceBooksMst.id)

        chapters.each {chapter ->
            copyChapter(""+chapter.id,""+destBooksMst.id,destBooksMst.siteId)
        }

        copyGptContents(""+destBooksMst.id)
        return destBooksMst.id
    }

    def copyChapter(String sourceChapterId,String destBookId,Integer siteId){
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(sourceChapterId))
        BooksMst destBooksMst = dataProviderService.getBooksMst(new Long(destBookId))
        BooksMst sourceBooksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        if(destBooksMst.publisherId!=null&&sourceBooksMst.publisherId!=null&&destBooksMst.publisherId.intValue()==sourceBooksMst.publisherId.intValue()) {
            ChaptersMst chaptersMst1 = ChaptersMst.findByBookIdAndSortOrderIsNotNull(new Long(new Long(destBookId)),[sort: "sortOrder", order: "desc"])
            ChaptersMst newChaptersMst = new ChaptersMst()
            newChaptersMst.bookId = new Long(destBookId)
            newChaptersMst.name = chaptersMst.name
            newChaptersMst.previewChapter = chaptersMst.previewChapter
            newChaptersMst.chapterDesc = chaptersMst.chapterDesc
            newChaptersMst.coverImage1 = chaptersMst.coverImage1
            if(chaptersMst1 != null) newChaptersMst.sortOrder=chaptersMst1.sortOrder+1;
            newChaptersMst.save(failOnError: true, flush: true)
            dataProviderService.getChaptersList(new Long(destBookId))
            List resources = ResourceDtl.findAllByChapterIdAndSharingIsNull(new Long(sourceChapterId))

            resources.each { resource ->
                copyQuiz("" + resource.id, "" + newChaptersMst.id,destBookId,null, siteId)
            }
            return "Success"
        }else{
            return "Chapter can be copied to the book of same publisher"
        }
    }

    def copyQuiz(String resId,String chapterId,String bookId,String reAttempt,Integer siteId) {
        if(resId!=null&&!"".equals(resId)) {
            ResourceDtl resourceDtl = ResourceDtl.findById(new Long(resId))
            ResourceDtl resourceDtlInstance = new ResourceDtl()
            if("true".equals(reAttempt)) resourceDtlInstance.allowReAttempt ="Yes" else resourceDtlInstance.allowReAttempt ="No"
            resourceDtlInstance.resLink = resourceDtl.resLink
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = resourceDtl.resType
            resourceDtlInstance.chapterId = new Long(chapterId)
            resourceDtlInstance.resourceName = resourceDtl.resourceName
            resourceDtlInstance.examSyllabus = resourceDtl.examSyllabus
            resourceDtlInstance.grade = resourceDtl.grade
            resourceDtlInstance.language1 = resourceDtl.language1
            resourceDtlInstance.language2 = resourceDtl.language2
            resourceDtlInstance.examSubject = resourceDtl.examSubject
            resourceDtlInstance.examId = resourceDtl.examId
            resourceDtlInstance.quizMode = resourceDtl.quizMode
            resourceDtlInstance.chapterDesc = resourceDtl.chapterDesc
            resourceDtlInstance.filename = resourceDtl.filename
            resourceDtlInstance.modifiedFile = resourceDtl.modifiedFile
            resourceDtlInstance.parentId = resourceDtl.parentId==null?resourceDtl.id:resourceDtl.parentId
            resourceDtlInstance.siteId = siteId
            resourceDtlInstance.downloadlink1 = resourceDtl.downloadlink1
            resourceDtlInstance.downloadlink2 = resourceDtl.downloadlink2
            resourceDtlInstance.downloadlink3 = resourceDtl.downloadlink3
            resourceDtlInstance.videoPlayer = resourceDtl.videoPlayer
            resourceDtlInstance.allowComments = resourceDtl.allowComments
            resourceDtlInstance.displayComments = resourceDtl.displayComments
            resourceDtlInstance.gptResourceType = resourceDtl.gptResourceType
            resourceDtlInstance.save(failOnError: true, flush: true)


            //updating resLink with new chapterId and bookId
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            resourceDtlInstance.resLink = resourceDtl.resLink.replace("/"+resId+"/","/"+resourceDtlInstance.id.toString()+"/")
                    .replace(resourceDtl.chapterId.toString(),chapterId)
                    .replace(chaptersMst.bookId.toString(),bookId)

            resourceDtlInstance.save(failOnError: true, flush: true)

            //copying the relative upload folder
            if(resourceDtlInstance.resType.equals("Notes")) {
                if("supload/dummy.pdf".equals(resourceDtl.resLink)) {
                    resourceDtlInstance.resLink = "supload/dummy.pdf"
                    resourceDtlInstance.save(failOnError: true, flush: true)
                }else {
                    try {
                        File srcDir = new File(grailsApplication.config.grails.basedir.path + resourceDtl.resLink.substring(0, resourceDtl.resLink.lastIndexOf("/") + 1))
                        File destDir = new File(grailsApplication.config.grails.basedir.path + resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.lastIndexOf("/") + 1))

                        if (!destDir.exists()) destDir.mkdirs()
                        FileUtils.copyDirectory(srcDir, destDir)
                    } catch (IOException e) {
                        println "error while copying.. " + e.toString()
                    }
                }
            }else if(resourceDtlInstance.resType.equals("KeyValues")){
                List keyValuesList = KeyValues.findAllByResId(resourceDtl.id)
                 keyValuesList.each {keyValue ->
                     KeyValues keyValues = new KeyValues()
                     keyValues.resId = resourceDtlInstance.id
                     keyValues.term = keyValue.term
                     keyValues.definition = keyValue.definition
                     keyValues.status = keyValue.status
                     keyValues.save(failOnError: true, flush: true)

                 }
            }

            if(resourceDtlInstance.sharing==null&&resourceDtlInstance.chapterId!=null){
                 if(chaptersMst!=null) {

                    BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
                    if(booksMst!=null&&!"true".equals(booksMst.hasQuiz)){
                        booksMst.hasQuiz="true"
                        booksMst.save(failOnError: true, flush: true)
                        BooksMst.wsuser.executeUpdate("update BooksMst set hasQuiz='true' where id="+booksMst.id)
                        BooksMst.wsshop.executeUpdate("update BooksMst set hasQuiz='true' where id="+booksMst.id)
                    }
                }
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
              //  dataProviderService.getLiveTests(siteId)
            } else {
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }
        }

    }

    def copyGptContents(String bookId){
        List chapters = ChaptersMst.findAllByBookId(new Integer(bookId))
        chapters.each {chapter ->
            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndGptResourceTypeIsNull(new Integer(""+chapter.id),"Notes")
            if(resourceDtl!=null){
                List resources = ResourceDtl.findAllByChapterIdAndGptResourceTypeIsNotNull(new Integer(""+chapter.id))
                resources.each{resource ->
                    if(resource.parentId!=null) {
                        GptDefaultCreateLog newGptLog = GptDefaultCreateLog.findByResId(resource.id)
                        if(newGptLog==null) {
                            GptDefaultCreateLog gptLog = GptDefaultCreateLog.findByResId(resource.parentId)
                            //create new GptDefaultCreateLog and copy the content
                            if(gptLog!=null) {
                                newGptLog = new GptDefaultCreateLog(username: gptLog.username, prompt: gptLog.prompt, response: gptLog.response, response2: gptLog.response2, promptType: gptLog.promptType, resId: resource.id, readingMaterialResId: resourceDtl.id, promptLabel: gptLog.promptLabel)
                                newGptLog.save(flush: true)
                            }
                        }
                    }
                }
                gptLogService.getGPTResources(new Long(""+resourceDtl.id))
            }

        }
    }

    // reading materials - uploaded content

    def addFile(params,flash,siteId){
        String uploadFolder="upload"
        def file = params.file
        def resourceDtlInstance = new ResourceDtl()
        def chapterId = params.chapterId
        BooksMst booksMst =  dataProviderService.getBooksMst(new Long(params.bookId))
        if("Yes".equals(booksMst.newStorage)) uploadFolder = "supload"
        if(file.empty) {
            flash.message = "File cannot be empty"
        } else {


            String filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            resourceDtlInstance.filename = filename
            resourceDtlInstance.siteId = siteId
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "Notes"
            resourceDtlInstance.chapterId = new Integer(chapterId)
            resourceDtlInstance.resourceName = (params.resourceName != null ? params.resourceName : file.originalFilename)
            resourceDtlInstance.quizMode = params.quizMode
            resourceDtlInstance.noOfPages = 0
            resourceDtlInstance.resLink = "blank"
            resourceDtlInstance.resSubType = params.subType
            resourceDtlInstance.zoomLevel = params.zoomLevel?params.zoomLevel:null
            if ("file".equals(params.quizMode)) {
                resourceDtlInstance.sharing="createdbyuser"
            }

            resourceDtlInstance.save(flush: true, failOnError: true)


            def resourceId = resourceDtlInstance.id
            boolean isZipBook
            if(filename.endsWith(".pdf"))
            {
                resourceDtlInstance.resLink = uploadFolder+"/books/" + params.bookId + "/chapters/" + chapterId + "/" + resourceId + "/" + resourceDtlInstance.id+".pdf"
                resourceDtlInstance.filename = ""+resourceDtlInstance.id+".pdf"
            }
            else resourceDtlInstance.resLink = uploadFolder+"/books/" + params.bookId + "/chapters/" + chapterId + "/" + resourceId + "/" + resourceDtlInstance.filename

            File uploadDir = new File(grailsApplication.config.grails.basedir.path +"/"+uploadFolder+ "/books/" + params.bookId + "/chapters/" + chapterId + "/" + resourceId)
            if(!uploadDir.exists()) {
                println("dir doesn't exist, so creating")
                uploadDir.mkdirs()
            }
            file.transferTo(new File(grailsApplication.config.grails.basedir.path +"/"+ resourceDtlInstance.resLink))
            if("Uploaded Media".equals(params.resourceType)){
                resourceDtlInstance.resType = "Uploaded Media"
            }
            else if ("true".equals(params.convert)) {
                resourceDtlInstance.videoPlayer = "no"
            }
            else if ("file".equals(params.quizMode)||"false".equals(params.convert)) {
                resourceDtlInstance.videoPlayer = params.pdf_download
            } else {
                boolean isEpub = resourceDtlInstance.filename.endsWith(".epub")
                isZipBook = resourceDtlInstance.filename.endsWith(".zip")
                resourceDtlInstance.modifiedFile = ((int) (Math.random() * 101)) + filename.substring(0, filename.indexOf(".")) + ((int) (Math.random() * 501)) + (isEpub ? ".epub" : ".zip")

                File extractDir

                if(isEpub){

                    BooksMst.executeUpdate("update BooksMst set singleEpub =null where id=" + params.bookId)
                    BooksMst.wsshop.executeUpdate("update BooksMst set singleEpub =null where id=" + params.bookId)
                    BooksMst.wsuser.executeUpdate("update BooksMst set singleEpub =null where id=" + params.bookId)
                }




                resourceDtlInstance.save(flush: true, failOnError: true)


                if (isZipBook) {
                    extractDir = new File(uploadFolder+"/books/" + params.bookId + "/chapters/" + chapterId + "/" + resourceId + "/extract/")
                    //delete upload image directory to make sure previous generated images are flushed
                    if (extractDir.exists()) deleteDirectory(extractDir.getAbsolutePath())
                    extractDir.mkdirs()
                    unzip(resourceDtlInstance.resLink, extractDir.getAbsolutePath(), false, resourceId, isZipBook)
                }

                if (isZipBook) { //isEpub ||
                    def sql

                        sql = "delete from resource_dtl_sub where resource_id=" + resourceDtlInstance.id +
                                " and (filename like ('%Cover.html') or filename like ('%index.html'))"

                    def dataSource = grailsApplication.mainContext.getBean('dataSource')
                    def sql1 = new Sql(dataSource)
                    sql1.execute sql

                    zip(extractDir.getAbsolutePath(), resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.lastIndexOf("/") + 1) + resourceDtlInstance.modifiedFile)
                }
            }
            resourceDtlInstance.save(flush: true, failOnError: true)
            if (resourceDtlInstance.sharing == null) {
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            } else {
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
            }
        }
        return chapterId
    }

    //Editing the zoom level
    def editPdfZoomLevel(params){
        ResourceDtl resourceDtlInstance
        if ("edit".equals(params.mode)){
            resourceDtlInstance = dataProviderService.getResourceDtl(new Long(params.resourceDtlId))
            resourceDtlInstance.zoomLevel = params.zoomLevel
            resourceDtlInstance.save(flush: true, failOnError: true)
            return ["status":"OK","zoomLevel": resourceDtlInstance.zoomLevel]
        }
    }

    def addIndependentContentFile(params,flash,siteId){
        def file = params.file
        def resourceDtlInstance = new ResourceDtl()
        def resourceId
        if(file.empty) {
            flash.message = "File cannot be empty"
        } else {
            String filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            resourceDtlInstance.filename = filename
            resourceDtlInstance.siteId = siteId
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "Notes"
            resourceDtlInstance.resourceName = (params.resourceName != null ? params.resourceName : file.originalFilename)
            resourceDtlInstance.quizMode = params.quizMode
            resourceDtlInstance.noOfPages = 0
            resourceDtlInstance.resLink = "blank"
            resourceDtlInstance.resSubType = params.subType
            resourceDtlInstance.indContentType = params.indContentType?params.indContentType:null
            if ("file".equals(params.quizMode)) {
                resourceDtlInstance.sharing="createdbyuser"
            }

            resourceDtlInstance.save(flush: true, failOnError: true)

            resourceId = resourceDtlInstance.id
            boolean isZipBook

            resourceDtlInstance.resLink = "supload/independentcontent/resId/" + resourceId + "/" + resourceDtlInstance.filename

            File uploadDir = new File(grailsApplication.config.grails.basedir.path + "supload/independentcontent/resId/" + resourceId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path + resourceDtlInstance.resLink))
            if(filename.indexOf(".mp4")>-1||filename.indexOf(".mp3")>-1||filename.indexOf(".wav")>-1){
                resourceDtlInstance.resType = "Uploaded Media"
            }
            else if ("file".equals(params.quizMode)||"false".equals(params.convert)) {
                resourceDtlInstance.videoPlayer = params.pdf_download

                resourceDtlInstance.save(flush: true, failOnError: true)
            } else {
                boolean isEpub = resourceDtlInstance.filename.endsWith(".epub")
                isZipBook = resourceDtlInstance.filename.endsWith(".zip")
                resourceDtlInstance.modifiedFile = ((int) (Math.random() * 101)) + filename.substring(0, filename.indexOf(".")) + ((int) (Math.random() * 501)) + (isEpub ? ".epub" : ".zip")

                File extractDir

                if(!isEpub && !isZipBook) {
                    def pdfFile = resourceDtlInstance.resLink

                    extractDir = new File("supload/independentcontent/resId/" + resourceId + "/extract/")
                    //delete upload image directory to make sure previous generated images are flushed
                    if (extractDir.exists()) deleteDirectory(extractDir.getAbsolutePath())
                    extractDir.mkdirs()

                    def scriptCom = ['java', '-Xms256M', '-Xmx512M', '-jar', "${grailsApplication.config.grails.basedir.path}pdf2html5.jar", "${pdfFile}", "${extractDir.getAbsolutePath()}"]
                    //def scriptCom = "java -Xms256M -Xmx512M -jar ${grailsApplication.config.grails.basedir.path}/pdf2html5.jar ${pdfFile} ${extractDir.getAbsolutePath()}"
                    def proc = scriptCom.execute()

                    //kill after 60 minutes if still running
                    proc.waitForOrKill(3600000)

                    if (proc.exitValue() != 0) {
                        println "[[return code: ${proc.exitValue()}]]"
                        println "[[stderr: ${proc.err.text}]]"
                    }

                    resourceDtlInstance.resLink = resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.indexOf(".")) + ".zip"
                    zip(extractDir.getAbsolutePath(), resourceDtlInstance.resLink)

                    isZipBook = true
                }


                resourceDtlInstance.save(flush: true, failOnError: true)

                if (isZipBook) {
                    extractDir = new File("supload/independentcontent/resId/" + resourceId + "/extract/")
                    //delete upload image directory to make sure previous generated images are flushed
                    if (extractDir.exists()) deleteDirectory(extractDir.getAbsolutePath())
                    extractDir.mkdirs()
                    unzip(resourceDtlInstance.resLink, extractDir.getAbsolutePath(), false, resourceId, isZipBook)
                }

                if (isZipBook) { //isEpub ||
                    def sql
                    sql = "delete from resource_dtl_sub where resource_id=" + resourceDtlInstance.id +
                            " and (filename like ('%Cover.html') or filename like ('%index.html'))"

                    def dataSource = grailsApplication.mainContext.getBean('dataSource')
                    def sql1 = new Sql(dataSource)
                    sql1.execute sql

                    zip(extractDir.getAbsolutePath(), resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.lastIndexOf("/") + 1) + resourceDtlInstance.modifiedFile)
                }
            }
            if (resourceDtlInstance.sharing == null) {
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
            }
            if(params.indContentType!=null && !"".equals(params.indContentType)) {
                redisService.("getindependentResourcDetails" + params.page + "_" + siteId) = null
                redisService.("getindependentResourcDetails" + params.page + "_" + siteId + "_recordCount") = null
            dataNotificationService.independentResourceUpdated(resourceDtlInstance.id,siteId)
            }
        }
        return resourceId
    }

    Element getNodeWithAttribute(Object root, String attrName, String attrValue) {
        NodeList nl = root.getChildNodes()

        for (int i = 0; i < nl.getLength(); i++) {
            Node n = nl.item(i)

            if (n instanceof Element) {
                Element el = (Element) n

                if (el.getAttribute(attrName).equals(attrValue)) {
                    return el
                } else {
                    el =  getNodeWithAttribute(n, attrName, attrValue) //search recursively

                    if(el!=null) return el
                }
            }
        }

        return null
    }
    def zip(String dir, String zipFileName) {
        try {
            filesListInDir = new ArrayList<String>()
            populateFilesList(dir)

            //now zip files one by one
            //create ZipOutputStream to write to the zip file
            FileOutputStream fos = new FileOutputStream(zipFileName)
            ZipOutputStream zos = new ZipOutputStream(fos)

            for(String filePath : filesListInDir){
                //System.out.println("Zipping "+filePath)
                //for ZipEntry we need to keep only relative file path, so we used substring on absolute path
                ZipEntry ze = new ZipEntry(filePath.substring(dir.length()+1, filePath.length()))
                zos.putNextEntry(ze)

                //read the file and write to ZipOutputStream
                FileInputStream fis = new FileInputStream(filePath)
                byte[] buffer = new byte[1024]
                int len

                while ((len = fis.read(buffer)) > 0) {
                    zos.write(buffer, 0, len)
                }

                zos.closeEntry()
                fis.close()
            }

            zos.close()
            fos.close()

            //delete the archive directory
            //deleteDirectory(dir)
        } catch (IOException e) {
            e.printStackTrace()
        }
    }

    def deleteDirectory(String dirStr) {
        File dir = new File(dirStr)
        if (dir.isDirectory()) {
            File[] children = dir.listFiles()
            for (int i = 0 ;i < children.length; i++) {
                boolean success = deleteDirectory(children[i].getAbsolutePath())
                if (!success) {
                    return false
                }
            }
        }

        // either file or an empty directory
        //System.out.println("removing file or directory : " + dir.getName())
        return dir.delete()
    }

    def unzip(String zipFilePath, String destDir, boolean delDesDir, Long resourceId, boolean isZipBook) {
        File dir = new File(destDir)

        if(dir.exists() && delDesDir) deleteDirectory(destDir)
        dir.mkdirs()

        try {
            ZipFile zipFile = new ZipFile(zipFilePath)
            Enumeration<?> enu = zipFile.entries()

            while (enu.hasMoreElements()) {
                ZipEntry zipEntry = (ZipEntry) enu.nextElement()

                String name = zipEntry.getName().replace("OPS/","OEBPS/")
                File file = new File(destDir+File.separator+name)

                if(name.endsWith("/")) {
                    file.mkdirs()
                    continue
                } else if(name.endsWith(".opf")) {
                    opfFileName = name
                }

                File parent = file.getParentFile()

                if (parent!=null) {
                    parent.mkdirs()
                }

                InputStream is = zipFile.getInputStream(zipEntry)
                ByteArrayOutputStream buffer
                FileOutputStream fos

                if(isZipBook && name.endsWith(".html")) buffer = new ByteArrayOutputStream()
                else fos = new FileOutputStream(file)

                byte[] bytes = new byte[1024]

                int length
                while ((length = is.read(bytes)) >= 0) {
                    if(isZipBook && name.endsWith(".html"))
                        buffer.write(bytes, 0, length)
                    else
                        fos.write(bytes, 0, length)
                }

                if(isZipBook && name.endsWith(".html")) {
                    def resourceDtlSub = new ResourceDtlSub()
                    resourceDtlSub.filename = java.net.URLDecoder.decode(name,"UTF-8").replace("\\","/")

                    resourceDtlSub.filedata = buffer.toByteArray()
                    resourceDtlSub.resourceId = resourceId
                    resourceDtlSub.save(flush: true, failOnError: true)
                }

                if(isZipBook && name.endsWith(".html"))
                    buffer.close()
                else
                    fos.close()

                is.close()
            }

            if(isZipBook) {
                def sql = "select filedata,filename from resource_dtl_sub where resource_id=" + resourceId
                sql+=" order by CONVERT(replace(replace(filename,substr(filename,1,INSTR(filename, '/')),''),'.html',''),UNSIGNED INTEGER)"
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource)
                def dataList = sql1.rows(sql)

                byte[] filedata=null,temp

                dataList.each { data ->
                    if(filedata==null) {
                        filedata = data[0]
                    } else {
                        temp = new byte[filedata.length + data[0].length]

                        System.arraycopy(filedata, 0, temp, 0, filedata.length)
                        System.arraycopy(data[0], 0, temp, filedata.length, data[0].length)

                        filedata = temp
                    }
                }

                if(filedata) {
                    Files.write(Paths.get(zipFilePath.substring(0, zipFilePath.lastIndexOf(".")+1)+"ws"), filedata)

                    if(!System.properties['os.name'].toLowerCase().contains('windows') && !System.properties['os.name'].toLowerCase().contains('mac')
                            && !zipFilePath.substring(0, zipFilePath.lastIndexOf(".")).toLowerCase().endsWith("-noopt")) {
                        def scriptCom = "${grailsApplication.config.grails.basedir.path}/optimiseRes.sh ${zipFilePath.substring(0, zipFilePath.lastIndexOf("/") + 1)}"
                        def proc = scriptCom.execute()

                        //kill after 10 minutes if still running
                        proc.waitForOrKill(600000)

                        if (proc.exitValue() != 0) {
                            println "[[return code: ${proc.exitValue()}]]"
                            println "[[stderr: ${proc.err.text}]]"
                        }
                        //else {
                        //    println proc.text
                        //}
                    }
                }
            }

            zipFile.close()

        } catch (IOException e) {
            e.printStackTrace()
        }
    }

    def parseXmlFile(String filePath){
        //get the factory
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance()

        try {

            //Using factory get an instance of document builder
            DocumentBuilder db = dbf.newDocumentBuilder()

            //parse using builder to get DOM representation of the XML file
            dom = db.parse(filePath)
            v = new Vector<String>()

            //System.out.println("Root element :" + dom.getDocumentElement().getNodeName())

            if (dom.hasChildNodes()) {
                printNote(dom.getChildNodes())
            }
        }catch(ParserConfigurationException pce) {
            pce.printStackTrace()
        }catch(SAXException se) {
            se.printStackTrace()
        }catch(IOException ioe) {
            ioe.printStackTrace()
        }
    }

    def populateFilesList(String dirName) throws IOException {
        for(File file : (new File(dirName)).listFiles()){
            if(file.isFile()) filesListInDir.add(file.getAbsolutePath())
            else populateFilesList(file.getAbsolutePath())
        }
    }

    void printNote(NodeList nodeList) {
        for (int count = 0 ;count < nodeList.getLength(); count++) {
            Node tempNode = nodeList.item(count)

            // make sure it's element node.
            if (tempNode.getNodeType() == Node.ELEMENT_NODE) {
                if(!wantedNode && tempNode.getNodeName().equals("spine")) {
                    wantedNode = true
                }

                if(wantedNode) {
                    // get node name and value
                    //System.out.println("\nNode Name =" + tempNode.getNodeName() + " [OPEN]")
                    //System.out.println("Node Value =" + tempNode.getTextContent())


                    if (tempNode.hasAttributes()) {
                        // get attributes names and values
                        NamedNodeMap nodeMap = tempNode.getAttributes()

                        for (int i = 0 ;i < nodeMap.getLength(); i++) {
                            Node node = nodeMap.item(i)

                            if(wantedNode && tempNode.getNodeName().equals("itemref") && node.getNodeName().equals("idref")) {
                                //System.out.println("attr name : " + node.getNodeName())
                                //System.out.println("attr value : " + node.getNodeValue())
                                v.add(node.getNodeValue())
                            }
                        }
                    }
                }

                if(tempNode.hasChildNodes()) {
                    // loop again if has child nodes
                    printNote(tempNode.getChildNodes())
                }

                if(wantedNode) {
                    //System.out.println("Node Name =" + tempNode.getNodeName() + " [CLOSE]")
                    if(tempNode.getNodeName().equals("spine")) break
                }
            }
        }
    }

    //Reading materials -created

    @Secured(['ROLE_USER']) @Transactional
    def addHTML(params,session,siteId) {

        ResourceDtl resourceDtlInstance
        String fileData
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)

        if ("create".equals(params.mode)) {
            if (session.getAttribute("htmlId") != null)
                resourceDtlInstance = ResourceDtl.findById(new Integer(session.getAttribute("htmlId") + ""))
            else
                resourceDtlInstance = new ResourceDtl()

            resourceDtlInstance.createdBy = springSecurityService.currentUser.username;
            resourceDtlInstance.dateCreated = new Date()
            resourceDtlInstance.resLink = "empty"
            resourceDtlInstance.resType = params.resourceType
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.resSubType = params.subType
            resourceDtlInstance.gptResourceType = params.gptResourceType
            if(params.subject!=null) resourceDtlInstance.subject = params.subject

            if(params.testStartDate!=null&&!"".equals(params.testStartDate)&&!" ".equals(params.testStartDate)) {
                DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date columnValue = df1.parse(params.testStartDate);
                columnValue = utilService.convertDate(columnValue,"IST","UTC")
                resourceDtlInstance.testStartDate = columnValue
            }

            if(!user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
            }){
                resourceDtlInstance.sharing="createdbyuser"
            }
            resourceDtlInstance.save(failOnError: true, flush: true)
            resourceDtlInstance.filename = "res"+resourceDtlInstance.id
            resourceDtlInstance.siteId = siteId

            //update the resLink
            if("notes".equals(params.page)){
                resourceDtlInstance.chapterId = new Integer(params.chapterId)
                resourceDtlInstance.resLink = "supload/books/" + params.bookId + "/chapters/" + params.chapterId + "/" + resourceDtlInstance.id + "/" + "res"+resourceDtlInstance.id
            }
            else {
                resourceDtlInstance.quizMode = params.page
                resourceDtlInstance.resLink = "supload/resources/" + resourceDtlInstance.id+"/res"+resourceDtlInstance.id
            }
            resourceDtlInstance.save(failOnError: true, flush: true)

            session.removeAttribute("htmlId")

            //logic to move automatically extracted images to the right place
            if("notes".equals(params.page)) {
               if (resourceDtlInstance.sharing == null) {
                    dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                    dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

                } else {
                    dataProviderService.getChapterResourcesForUser(new Long(params.chapterId), springSecurityService.currentUser.username)
                }
                resourceDtlInstance.resLink = "supload/books/" + params.bookId + "/chapters/" + params.chapterId + "/" + resourceDtlInstance.id + "/" + "res"+resourceDtlInstance.id
                fileData = moveExtractedImages(params.bookId, params.chapterId, resourceDtlInstance.id, params.notes,params.page)
            }else{
                if("syllabus".equals(params.page))  SyllabusGradeDtl.executeUpdate("update SyllabusGradeDtl set syllabusResId=" + resourceDtlInstance.id + " where id=" + params.gradeid)
                fileData = moveExtractedImages(params.bookId, params.chapterId, resourceDtlInstance.id, params.notes,params.page)

            }

            resourceDtlInstance.save(failOnError: true, flush: true)
            if(params.folderId!=null&&!"".equals(params.folderId)) {
               folderService.addResourceToFolder(params.folderId,""+resourceDtlInstance.id)
            }
            session.removeAttribute("htmlId")

        } else if ("edit".equals(params.mode)) {
            resourceDtlInstance = dataProviderService.getResourceDtl(new Long(params.resourceDtlId))

            resourceDtlInstance.dateCreated = new Date()
            resourceDtlInstance.filename = "res"+resourceDtlInstance.id
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username;
            resourceDtlInstance.resSubType = params.subType
            if(params.subject!=null) resourceDtlInstance.subject = params.subject
           if(params.testStartDate!=null&&!"".equals(params.testStartDate)&&!" ".equals(params.testStartDate)) {
                DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                Date columnValue = df1.parse(params.testStartDate);
                columnValue = utilService.convertDate(columnValue,"IST","UTC")
                resourceDtlInstance.testStartDate = columnValue
            }
            if("notes".equals(params.page)) {
                resourceDtlInstance.resLink = "supload/books/" + params.bookId + "/chapters/" + params.chapterId + "/" + resourceDtlInstance.id + "/" + "res"+resourceDtlInstance.id
                resourceDtlInstance.save(failOnError: true, flush: true)
                fileData = moveExtractedImages(params.bookId, params.chapterId, resourceDtlInstance.id, params.notes,params.page)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                if ("published".equals(booksMst.status)) {
                    dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
                    dataNotificationService.readingMaterialUpdated(chaptersMst.id, chaptersMst.bookId, resourceDtlInstance.id)
                }
            }else{
                resourceDtlInstance.quizMode=params.page
                resourceDtlInstance.resLink = "supload/resources/" + resourceDtlInstance.id+"/res"+resourceDtlInstance.id
                resourceDtlInstance.save(failOnError: true, flush: true)
                fileData = moveExtractedImages(params.bookId, params.chapterId, resourceDtlInstance.id, params.notes,params.page)

            }


            session.removeAttribute("htmlId")



        }

        if (fileData) {
            File uploadDir
            if ("notes".equals(params.page)) {
                uploadDir = new File("supload/books/" + params.bookId + "/chapters/" + params.chapterId + "/" + resourceDtlInstance.id + "/extract/")

            } else {
                uploadDir = new File("supload/resources/" + resourceDtlInstance.id +"/res"+resourceDtlInstance.id+ "/extract/")
            }

            if (!uploadDir.exists()) uploadDir.mkdirs()
            Files.write(Paths.get(resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.length()) + ".ws"), fileData.getBytes("UTF-8"))

            File zipFile = new File(resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.length()) + "_zippied.zip")
            if (zipFile.exists()) zipFile.delete()

            if (!System.properties['os.name'].toLowerCase().contains('windows') && !System.properties['os.name'].toLowerCase().contains('mac')) {
                def scriptCom = "${grailsApplication.config.grails.basedir.path}/optimiseRes.sh ${resourceDtlInstance.resLink.substring(0, resourceDtlInstance.resLink.lastIndexOf("/") + 1)}"
                def proc = scriptCom.execute()

                //kill after 10 minutes if still running
                proc.waitForOrKill(600000)

                if (proc.exitValue() != 0) {
                    println "[[return code: ${proc.exitValue()}]]"
                    println "[[stderr: ${proc.err.text}]]"
                }
            }
        }

        if(!"notes".equals(params.page)) {
            redisService.("getindependentResourcDetails"+params.page+"_"+siteId) = null
            redisService.("getindependentResourcDetails"+params.page+"_"+siteId+"_recordCount") = null
            dataNotificationService.independentResourceUpdated(resourceDtlInstance.id, siteId)
        }

            dataProviderService.getUserResources("Notes", springSecurityService.currentUser.username)
            dataProviderService.getUserResources("all", springSecurityService.currentUser.username)

       return resourceDtlInstance.id

    }



    String moveExtractedImages(String bookId,String chapterId,Long resourceDtlId,String notes,String pageType){
        boolean extractedImageExists = true
        String fileData = notes
        int startingIndex = 0
        int startIndex = 0
        while(extractedImageExists){
            if(fileData.indexOf("imagesfrompdf",startingIndex)!=-1){

                //Step1 . extract the source file path

                int startSourceIndex = fileData.indexOf("source=",startIndex)
                String src = fileData.substring((startSourceIndex+7),(fileData.indexOf('.png',startSourceIndex)+4))

                File srcFile= new File(src)
                startIndex = fileData.indexOf("imagesfrompdf/",startingIndex)
                String fileName = fileData.substring((startIndex+14),(fileData.indexOf('.png',startIndex)+4))

                //step 2. create destination path
                String destDir = "supload/books/"+bookId+"/chapters/"+chapterId+"/"+resourceDtlId+"/extract/OEBPS/Images/"
                File uploadDir = new File(destDir)
                if(!uploadDir.exists()) uploadDir.mkdirs()
                //step 3. copy file
                File dest = new File(destDir+fileName)
                Files.copy(srcFile.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING)
                //moving the index
                startingIndex = startIndex+14

            }else extractedImageExists=false
        }
        if("notes".equals(pageType)) {
            fileData = fileData.replace("/funlearn/downloadEpubImage?source=upload/books/" + bookId + "/chapters/" + chapterId + "/imagesfrompdf/", "Images/")
            fileData = fileData.replace("/funlearn/downloadEpubImage?source=upload/books/" + bookId + "/chapters/" + chapterId + "/" + resourceDtlId + "/extract/OEBPS/", "")
        }else{
              fileData = fileData.replace("/funlearn/downloadEpubImage?source=upload/resources/" +resourceDtlId + "/extract/OEBPS/", "")
        }
        return fileData
    }

    //links

    def addlink(params,request,session){
        def resourceDtlInstance = new ResourceDtl()
        resourceDtlInstance.resLink = params.link
        resourceDtlInstance.quizMode = params.quizMode?params.quizMode:null
        resourceDtlInstance.createdBy = springSecurityService.currentUser.username
        resourceDtlInstance.resType = params.resourceType
        if(params.chapterId!=null&&!"".equals(params.chapterId)) resourceDtlInstance.chapterId = new Integer(params.chapterId)
        if (params.resourceName.matches(".*[\\ \"\\<\\>\\{\\}|\\\\^~\\[\\]].*")) {
            resourceDtlInstance.resourceName =params.resourceName;
        }
        else resourceDtlInstance.resourceName =java.net.URLDecoder.decode(params.resourceName,"UTF-8");
        resourceDtlInstance.videoPlayer=params.videoPlayer
        resourceDtlInstance.allowComments=params.allowComments
        resourceDtlInstance.displayComments=params.displayComments
        resourceDtlInstance.downloadlink1 = params.downloadlink1
        resourceDtlInstance.downloadlink2 = params.downloadlink2
        resourceDtlInstance.downloadlink3 = params.downloadlink3
        resourceDtlInstance.indContentType = params.indContentType?params.indContentType:null
        resourceDtlInstance.siteId = utilService.getSiteId(request,session)
        Date startDate = null
        Date endDate = null
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm")

        if(params.resourceStartDate != null && params.resourceStartDate != "") {
            startDate = df.parse(params.resourceStartDate)
            startDate = utilService.convertDate(startDate,"IST","UTC")
            resourceDtlInstance.testStartDate = startDate
        }
        if(params.resourceEndDate != null && params.resourceEndDate != "") {
            endDate = df.parse(params.resourceEndDate)
            endDate = utilService.convertDate(endDate,"IST","UTC")
            resourceDtlInstance.testEndDate = endDate
        }

        User user = User.findByUsername(springSecurityService.currentUser.username)
        if(!user.authorities.any {
            it.authority == "ROLE_WS_CONTENT_CREATOR"||it.authority == "ROLE_BOOK_CREATOR"
        }){
            resourceDtlInstance.sharing="createdbyuser"
        }

        resourceDtlInstance.save(failOnError: true, flush: true)
        if(params.indContentType!=null && !"".equals(params.indContentType)) {
            redisService.("getindependentResourcDetails" + params.page + "_" + utilService.getSiteId(request,session)) = null
            redisService.("getindependentResourcDetails" + params.page + "_" + utilService.getSiteId(request,session) + "_recordCount") = null
           dataNotificationService.independentResourceUpdated(resourceDtlInstance.id, utilService.getSiteId(request,session))
        }
        if(resourceDtlInstance.sharing==null&&resourceDtlInstance.chapterId!=null){
            dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)

            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)


            if("Reference Videos".equals(params.resourceType)){
                Integer siteId = booksMst.siteId
                String siteIdList = siteId.toString()

                if (siteId.intValue() == 1) {
                    if (redisService.("siteIdList_" + siteId) == null) {
                        dataProviderService.getSiteIdList(siteId)
                    }

                    siteIdList = redisService.("siteIdList_" + siteId)
                }
                dataProviderService.getLatestVideos(siteIdList,siteId)
                dataProviderService.getLiveVideos(siteId)

                if(siteId.intValue()!=1){
                    dataProviderService.getLiveVideos(new Integer(1))
                }

            }
        } else {
            if(params.chapterId!=null&&!"".equals(params.chapterId)) dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            dataProviderService.getUserResources(resourceDtlInstance.resType,springSecurityService.currentUser.username)
            dataProviderService.getUserResources("all",springSecurityService.currentUser.username)
            if(params.folderId!=null&&!"".equals(params.folderId)&&!"null".equals(params.folderId)) folderService.addResourceToFolder(params.folderId,""+resourceDtlInstance.id)
        }

        return resourceDtlInstance.id
    }


    def updateResourceDtlsTag(bookId,syllabus,grade,subject){

        List chapters = ChaptersMst.findAllByBookId(bookId)

        chapters.each { chapter ->
            List resources = ResourceDtl.findAllByChapterId(chapter.id)
            resources.each { resource ->
                resource.syllabus = syllabus
                resource.grade = grade
                resource.subject = subject
                resource.save(failOnError: true, flush: true)

            }
        }
    }
    def updateSectionSubject(String from,String to,String subject,String quizId){
        List mcqs = ObjectiveMst.findAllByQuizId(quizId, [sort: "quizSort", order: "asc"])
        int fromIndex = Integer.parseInt(from)-1
        int toIndex = Integer.parseInt(to)
        for(int i=fromIndex;i<toIndex;i++){
            if(i<mcqs.size()){
                ObjectiveMst objectiveMst= mcqs[i]
                objectiveMst.subject = subject
                objectiveMst.save(failOnError: true, flush: true)
            }
        }
        println("updated questions from ${from} to ${to}");
    }

    def extractEpubContents(File epubFile, File uploadDir,params) {
        try {
            ZipInputStream zipInputStream = new ZipInputStream(new FileInputStream(epubFile))
            def entry = zipInputStream.nextEntry
            def chapterNamesList
            ArrayList chapterLinks = new ArrayList()
            ArrayList chapterHTMLRefs  = new ArrayList()
            while (entry != null) {
                if (!entry.isDirectory()) {
                    Integer entryLen = (entry.name.split("/").size()-1)
                    File outputFile = new File(uploadDir, entry.name.split("/")[entryLen]) // Replace slashes with underscores
                    FileOutputStream outputStream = new FileOutputStream(outputFile)
                    byte[] buffer = new byte[4096]
                    int bytesRead
                    while ((bytesRead = zipInputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead)
                    }
                    outputStream.close()
                    if(outputFile.name.endsWith(".xhtml") || outputFile.name.endsWith(".html")){
                        chapterLinks.add(outputFile.name)
                    }else if(outputFile.name.endsWith(".ncx")){
                        chapterNamesList = readNcxFile(outputFile)
                        chapterHTMLRefs = getPageLinks(outputFile)
                    }
                }
                entry = zipInputStream.nextEntry
            }
            zipInputStream.close()
            updateChapterInformation(chapterNamesList,chapterLinks,chapterHTMLRefs,params)
        } catch (Exception e) {
            println("Error extracting EPUB contents: ${e.message}")
        }
    }
    def readNcxFile(File ncxFile) {
        ArrayList chapterNames = new ArrayList()
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance()
            def docBuilder = factory.newDocumentBuilder()
            def doc = docBuilder.parse(ncxFile)

            // Find all text elements under navPoint that represent chapters
            def navPoints = doc.getElementsByTagName("navPoint")

            if(navPoints.length == 0){
                navPoints = doc.getElementsByTagName("ncx:navPoint")
            }
            navPoints.each { navPoint ->
                def textElement = navPoint.getElementsByTagName("text")
                if (textElement.length==0){
                    textElement = navPoint.getElementsByTagName("ncx:text")
                }
                if(textElement.length>0){
                    textElement = textElement.item(0)
                }
                if (textElement != null) {
                    chapterNames.add(textElement.getTextContent())
                }
            }
            return chapterNames
        } catch (Exception e) {
            println("Error reading NCX file: ${e.message}")
        }
    }
    def getPageLinks(File ncxFile){
        ArrayList linkElements = new ArrayList()
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance()
            def docBuilder = factory.newDocumentBuilder()
            def doc = docBuilder.parse(ncxFile)

            // Find all text elements under navPoint that represent chapters
            def navPoints = doc.getElementsByTagName("navPoint")

            if(navPoints.length == 0){
                navPoints = doc.getElementsByTagName("ncx:navPoint")
            }
            navPoints.each { navPoint ->
                def linkElement = navPoint.getElementsByTagName("content")
                if(linkElement.length==0){
                    linkElement = navPoint.getElementsByTagName("ncx:content")
                }

                if(linkElement.length>0){
                    linkElement=linkElement.item(0)
                }

                if(linkElement!=null){
                    String linkStr  = linkElement.getAttribute("src")
                    linkElements.add(linkStr)
                }
            }
            return linkElements
        } catch (Exception e) {
            println("Error reading NCX file: ${e.message}")
        }
    }
    def isContentFile(String filename) {
        !filename.toLowerCase().endsWith(".opf") && !filename.toLowerCase().endsWith(".ncx")
    }

    def updateChapterInformation(ArrayList chapNames,ArrayList chapLinks,ArrayList chapterHTMLRefs,params){
        if(chapNames.size()==chapLinks.size() || chapNames.size()==chapterHTMLRefs.size()){
            for(int i=0;i<chapNames.size();i++){
                ChaptersMst chaptersMst
                String filename =""
                String chapterName = chapNames[i]
                if(chapNames.size()==chapLinks.size()){
                    filename = chapLinks[i]
                }else if(chapNames.size()!=chapLinks.size() && chapterHTMLRefs.size()==chapNames.size()){
                    filename = chapterHTMLRefs[i]
                }

                filename = filename.replaceAll("\\s+", "")
                filename = filename.split('/')[-1]
                chaptersMst = new ChaptersMst(name:chapterName, bookId: new Long(params.bookId))
                chaptersMst.save(failOnError: true)
                def resourceDtlInstance = new ResourceDtl()

                resourceDtlInstance.filename = filename
                resourceDtlInstance.siteId = 1
                resourceDtlInstance.createdBy = springSecurityService.currentUser.username
                resourceDtlInstance.resType = "Notes"
                resourceDtlInstance.chapterId = chaptersMst.id
                resourceDtlInstance.resourceName = chapterName
                resourceDtlInstance.quizMode = params.quizMode
                resourceDtlInstance.noOfPages = 0
                resourceDtlInstance.resSubType = params.subType
                resourceDtlInstance.resLink = "supload/epub/"+params.bookId+"/"+filename
                resourceDtlInstance.ebupChapterLink =filename
                resourceDtlInstance.save(flush: true, failOnError: true)
            }
        }

    }

}
